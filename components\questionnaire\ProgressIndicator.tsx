import React, { useEffect } from 'react';
import { View, Text } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { useQuestionnaire } from '../../contexts/QuestionnaireContext';

export default function ProgressIndicator() {
  const { progress, gamification } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const progressValue = useSharedValue(0);
  const milestoneScale = useSharedValue(1);

  const themedColors = {
    background: isDark ? '#374151' : '#E5E7EB',
    progress: '#E5355F',
    milestone: '#10B981',
    text: isDark ? '#FFFFFF' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
  };

  // Update progress animation
  useEffect(() => {
    progressValue.value = withTiming(progress.percentage / 100, {
      duration: 800,
    });
  }, [progress.percentage]);

  // Milestone celebration animation
  useEffect(() => {
    const milestones = [25, 50, 75, 100];
    const currentMilestone = milestones.find(
      milestone => progress.percentage >= milestone && 
      progress.percentage < milestone + (100 / progress.totalSteps)
    );

    if (currentMilestone) {
      milestoneScale.value = withSpring(1.2, {}, () => {
        milestoneScale.value = withSpring(1);
      });
    }
  }, [progress.percentage]);

  const progressBarStyle = useAnimatedStyle(() => {
    return {
      width: `${interpolate(
        progressValue.value,
        [0, 1],
        [0, 100],
        Extrapolate.CLAMP
      )}%`,
    };
  });

  const milestoneStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: milestoneScale.value }],
    };
  });

  const renderStepDots = () => {
    const dots = [];
    for (let i = 1; i <= progress.totalSteps; i++) {
      const isCompleted = progress.completedSteps.includes(i);
      const isCurrent = progress.currentStep === i;
      
      dots.push(
        <View
          key={i}
          className={`w-3 h-3 rounded-full mx-1 ${
            isCompleted 
              ? 'bg-green-500' 
              : isCurrent 
                ? 'bg-red-500' 
                : isDark 
                  ? 'bg-gray-600' 
                  : 'bg-gray-300'
          }`}
        />
      );
    }
    return dots;
  };

  return (
    <View className="mb-4">
      {/* Main Progress Bar */}
      <View className="mb-3">
        <View 
          className="h-2 rounded-full overflow-hidden"
          style={{ backgroundColor: themedColors.background }}
        >
          <Animated.View
            className="h-full rounded-full"
            style={[
              { backgroundColor: themedColors.progress },
              progressBarStyle,
            ]}
          />
        </View>
      </View>

      {/* Progress Info */}
      <View className="flex-row items-center justify-between">
        {/* Step Dots */}
        <View className="flex-row items-center">
          {renderStepDots()}
        </View>

        {/* Percentage and Badges */}
        <View className="flex-row items-center">
          {/* Badge Count */}
          {gamification.badges.length > 0 && (
            <Animated.View 
              style={milestoneStyle}
              className="flex-row items-center mr-3"
            >
              <Text className="text-xs mr-1">🏆</Text>
              <Text 
                style={{ color: themedColors.textSecondary }}
                className="text-xs font-medium"
              >
                {gamification.badges.length}
              </Text>
            </Animated.View>
          )}

          {/* Percentage */}
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-sm font-medium"
          >
            {Math.round(progress.percentage)}%
          </Text>
        </View>
      </View>

      {/* Milestone Celebration */}
      {progress.percentage >= 25 && progress.percentage < 30 && (
        <Animated.View 
          style={milestoneStyle}
          className="mt-2 p-2 rounded-lg"
          style={{ backgroundColor: themedColors.milestone + '20' }}
        >
          <Text 
            style={{ color: themedColors.milestone }}
            className="text-xs font-medium text-center"
          >
            🎉 Great start! You're 25% done!
          </Text>
        </Animated.View>
      )}

      {progress.percentage >= 50 && progress.percentage < 55 && (
        <Animated.View 
          style={milestoneStyle}
          className="mt-2 p-2 rounded-lg"
          style={{ backgroundColor: themedColors.milestone + '20' }}
        >
          <Text 
            style={{ color: themedColors.milestone }}
            className="text-xs font-medium text-center"
          >
            🚀 Halfway there! You're doing amazing!
          </Text>
        </Animated.View>
      )}

      {progress.percentage >= 75 && progress.percentage < 80 && (
        <Animated.View 
          style={milestoneStyle}
          className="mt-2 p-2 rounded-lg"
          style={{ backgroundColor: themedColors.milestone + '20' }}
        >
          <Text 
            style={{ color: themedColors.milestone }}
            className="text-xs font-medium text-center"
          >
            ⭐ Almost done! Just a few more questions!
          </Text>
        </Animated.View>
      )}
    </View>
  );
}

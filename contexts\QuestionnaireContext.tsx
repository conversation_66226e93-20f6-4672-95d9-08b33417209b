import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  QuestionnaireData,
  GamificationState,
  QuestionnaireProgress,
  QuestionnaireContextType,
  Badge,
  PersonalityInsight,
} from '../types/questionnaire';

const QUESTIONNAIRE_STORAGE_KEY = 'questionnaire_data';
const GAMIFICATION_STORAGE_KEY = 'questionnaire_gamification';

const QuestionnaireContext = createContext<QuestionnaireContextType | undefined>(undefined);

const TOTAL_STEPS = 7;

// Initial states
const initialQuestionnaireData: QuestionnaireData = {
  startedAt: new Date(),
};

const initialGamificationState: GamificationState = {
  currentStep: 1,
  completedSteps: [],
  badges: [],
  insights: [],
  streak: 0,
  engagementScore: 0,
  timeSpent: 0,
  startTime: new Date(),
};

const initialProgress: QuestionnaireProgress = {
  currentStep: 1,
  totalSteps: TOTAL_STEPS,
  completedSteps: [],
  percentage: 0,
  estimatedTimeRemaining: 180, // 3 minutes in seconds
};

export const QuestionnaireProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [data, setData] = useState<QuestionnaireData>(initialQuestionnaireData);
  const [gamification, setGamification] = useState<GamificationState>(initialGamificationState);
  const [progress, setProgress] = useState<QuestionnaireProgress>(initialProgress);

  // Calculate progress percentage
  useEffect(() => {
    const percentage = (gamification.completedSteps.length / TOTAL_STEPS) * 100;
    const estimatedTimeRemaining = Math.max(0, 180 - gamification.timeSpent);
    
    setProgress(prev => ({
      ...prev,
      currentStep: gamification.currentStep,
      completedSteps: gamification.completedSteps,
      percentage,
      estimatedTimeRemaining,
    }));
  }, [gamification.completedSteps, gamification.currentStep, gamification.timeSpent]);

  // Update time spent
  useEffect(() => {
    const interval = setInterval(() => {
      if (gamification.startTime) {
        const timeSpent = Math.floor((Date.now() - gamification.startTime.getTime()) / 1000);
        setGamification(prev => ({ ...prev, timeSpent }));
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [gamification.startTime]);

  const updateStepData = (step: number, stepData: any) => {
    setData(prev => ({
      ...prev,
      [`step${step}`]: stepData,
    }));
    
    // Auto-save progress
    saveProgress();
  };

  const completeStep = (step: number) => {
    setGamification(prev => {
      const newCompletedSteps = [...prev.completedSteps];
      if (!newCompletedSteps.includes(step)) {
        newCompletedSteps.push(step);
      }
      
      return {
        ...prev,
        completedSteps: newCompletedSteps,
        currentStep: Math.min(step + 1, TOTAL_STEPS),
        engagementScore: prev.engagementScore + 10, // Increase engagement score
      };
    });

    // Auto-save progress
    saveProgress();
  };

  const goToStep = (step: number) => {
    if (step >= 1 && step <= TOTAL_STEPS) {
      setGamification(prev => ({
        ...prev,
        currentStep: step,
      }));
    }
  };

  const addBadge = (badge: Badge) => {
    setGamification(prev => {
      const existingBadge = prev.badges.find(b => b.id === badge.id);
      if (existingBadge) return prev;

      return {
        ...prev,
        badges: [...prev.badges, { ...badge, unlockedAt: new Date() }],
        engagementScore: prev.engagementScore + 25, // Bonus for badge
      };
    });
  };

  const addInsight = (insight: PersonalityInsight) => {
    setGamification(prev => {
      const existingInsight = prev.insights.find(i => i.id === insight.id);
      if (existingInsight) return prev;

      return {
        ...prev,
        insights: [...prev.insights, insight],
        engagementScore: prev.engagementScore + 5, // Small bonus for insight
      };
    });
  };

  const resetQuestionnaire = () => {
    const newStartTime = new Date();
    setData({ ...initialQuestionnaireData, startedAt: newStartTime });
    setGamification({ ...initialGamificationState, startTime: newStartTime });
    setProgress(initialProgress);
    
    // Clear stored data
    AsyncStorage.removeItem(QUESTIONNAIRE_STORAGE_KEY);
    AsyncStorage.removeItem(GAMIFICATION_STORAGE_KEY);
  };

  const saveProgress = async () => {
    try {
      await AsyncStorage.setItem(QUESTIONNAIRE_STORAGE_KEY, JSON.stringify(data));
      await AsyncStorage.setItem(GAMIFICATION_STORAGE_KEY, JSON.stringify(gamification));
    } catch (error) {
      console.error('Failed to save questionnaire progress:', error);
    }
  };

  const loadProgress = async () => {
    try {
      const savedData = await AsyncStorage.getItem(QUESTIONNAIRE_STORAGE_KEY);
      const savedGamification = await AsyncStorage.getItem(GAMIFICATION_STORAGE_KEY);

      if (savedData) {
        const parsedData = JSON.parse(savedData);
        // Convert date strings back to Date objects
        if (parsedData.startedAt) {
          parsedData.startedAt = new Date(parsedData.startedAt);
        }
        if (parsedData.completedAt) {
          parsedData.completedAt = new Date(parsedData.completedAt);
        }
        setData(parsedData);
      }

      if (savedGamification) {
        const parsedGamification = JSON.parse(savedGamification);
        // Convert date strings back to Date objects
        if (parsedGamification.startTime) {
          parsedGamification.startTime = new Date(parsedGamification.startTime);
        }
        parsedGamification.badges = parsedGamification.badges.map((badge: any) => ({
          ...badge,
          unlockedAt: new Date(badge.unlockedAt),
        }));
        setGamification(parsedGamification);
      }
    } catch (error) {
      console.error('Failed to load questionnaire progress:', error);
    }
  };

  const value: QuestionnaireContextType = {
    data,
    gamification,
    progress,
    updateStepData,
    completeStep,
    goToStep,
    addBadge,
    addInsight,
    resetQuestionnaire,
    saveProgress,
    loadProgress,
  };

  return (
    <QuestionnaireContext.Provider value={value}>
      {children}
    </QuestionnaireContext.Provider>
  );
};

export const useQuestionnaire = (): QuestionnaireContextType => {
  const context = useContext(QuestionnaireContext);
  if (context === undefined) {
    throw new Error('useQuestionnaire must be used within a QuestionnaireProvider');
  }
  return context;
};

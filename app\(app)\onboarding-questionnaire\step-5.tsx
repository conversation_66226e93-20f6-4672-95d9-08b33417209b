import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import QuestionnaireLayout from '../../../components/questionnaire/QuestionnaireLayout';
import VisualGrid from '../../../components/questionnaire/QuestionTypes/VisualGrid';
import SliderInput from '../../../components/questionnaire/QuestionTypes/SliderInput';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { QuestionnaireStep5Data } from '../../../types/questionnaire';
import { useColorScheme } from 'nativewind';

const budgetRanges = [
  { id: 'budget', label: 'Budget-Friendly', icon: 'dollar-sign', image: require('@/assets/images/onboarding1.png') },
  { id: 'moderate', label: 'Moderate', icon: 'credit-card', image: require('@/assets/images/onboarding2.png') },
  { id: 'generous', label: 'Generous', icon: 'gift', image: require('@/assets/images/onboarding3.png') },
  { id: 'luxury', label: 'Luxury', icon: 'star', image: require('@/assets/images/onboarding1.png') },
];

const valuePhilosophies = [
  {
    id: 'one-meaningful',
    label: 'One Meaningful Gift',
    icon: 'heart',
    image: require('@/assets/images/onboarding2.png')
  },
  {
    id: 'several-smaller',
    label: 'Several Smaller Gifts',
    icon: 'package',
    image: require('@/assets/images/onboarding3.png')
  },
  {
    id: 'experience-focused',
    label: 'Experience Over Things',
    icon: 'map-pin',
    image: require('@/assets/images/onboarding1.png')
  },
];

const spendingComforts = [
  { id: 'conservative', label: 'Conservative Spender', icon: 'shield', image: require('@/assets/images/onboarding2.png') },
  { id: 'moderate', label: 'Thoughtful Spender', icon: 'heart', image: require('@/assets/images/onboarding3.png') },
  { id: 'generous', label: 'Generous Giver', icon: 'gift', image: require('@/assets/images/onboarding1.png') },
];

export default function Step5() {
  const router = useRouter();
  const { data, updateStepData, completeStep, addBadge } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [currentStep, setCurrentStep] = useState(1); // 1: budget range, 2: value philosophy, 3: spending comfort
  const [stepData, setStepData] = useState<QuestionnaireStep5Data>({
    budgetRange: data.step5?.budgetRange || { min: 25, max: 100 },
    spendingComfort: data.step5?.spendingComfort || 'moderate',
    valuePhilosophy: data.step5?.valuePhilosophy || 'one-meaningful',
  });

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return stepData.budgetRange.min > 0 && stepData.budgetRange.max > stepData.budgetRange.min;
      case 2:
        return stepData.valuePhilosophy.length > 0;
      case 3:
        return stepData.spendingComfort.length > 0;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete step and move to next questionnaire step
      updateStepData(5, stepData);
      completeStep(5);

      // Award thoughtful giver badge
      addBadge({
        id: 'thoughtful-giver',
        name: 'Thoughtful Giver',
        description: 'Considered budget and value preferences',
        icon: '💝',
        category: 'progress',
        unlockedAt: new Date(),
      });

      router.push('/(app)/onboarding-questionnaire/step-6');
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const updateAnswer = (field: keyof QuestionnaireStep5Data, value: any) => {
    setStepData(prev => ({ ...prev, [field]: value }));
  };

  const recipientName = data.step1?.recipientName || 'them';

  const renderCurrentQuestion = () => {
    switch (currentStep) {
      case 1:
        return (
          <SliderInput
            title={`What's your typical gift budget for ${recipientName}?`}
            subtitle="This helps us suggest gifts in your comfort zone"
            min={10}
            max={500}
            step={5}
            isDual={true}
            formatValue={(val) => `$${val}`}
            onAnswer={(value) => {
              if (Array.isArray(value)) {
                updateAnswer('budgetRange', { min: value[0], max: value[1] });
              }
            }}
            currentAnswer={[stepData.budgetRange.min, stepData.budgetRange.max]}
            isRequired={true}
          />
        );

      case 2:
        return (
          <VisualGrid
            title={`When giving gifts to ${recipientName}, you prefer...`}
            subtitle="What's your gift-giving philosophy?"
            items={valuePhilosophies}
            columns={1}
            allowMultiple={false}
            onAnswer={(value) => updateAnswer('valuePhilosophy', value)}
            currentAnswer={stepData.valuePhilosophy}
            isRequired={true}
          />
        );

      case 3:
        return (
          <VisualGrid
            title="How would you describe your gift-giving style?"
            subtitle="This helps us understand your approach to spending"
            items={spendingComforts}
            columns={1}
            allowMultiple={false}
            onAnswer={(value) => updateAnswer('spendingComfort', value)}
            currentAnswer={stepData.spendingComfort}
            isRequired={true}
          />
        );

      default:
        return null;
    }
  };

  const getNextButtonText = () => {
    if (currentStep < 3) return "Continue";
    return "Next Step";
  };

  return (
    <QuestionnaireLayout
      title="Budget & Value Preferences"
      subtitle="Let's talk about gift-giving comfort zones"
      stepNumber={5}
      onNext={handleNext}
      onBack={handleBack}
      nextButtonText={getNextButtonText()}
      isNextDisabled={!isStepValid()}
      showBackButton={true}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        {renderCurrentQuestion()}

        {/* Progress indicator for sub-steps */}
        <View className="mt-6 flex-row justify-center">
          {[1, 2, 3].map((step) => (
            <View
              key={step}
              className={`w-2 h-2 rounded-full mx-1 ${
                step === currentStep
                  ? 'bg-red-500'
                  : step < currentStep
                    ? 'bg-green-500'
                    : isDark
                      ? 'bg-gray-600'
                      : 'bg-gray-300'
              }`}
            />
          ))}
        </View>
      </ScrollView>
    </QuestionnaireLayout>
  );
}

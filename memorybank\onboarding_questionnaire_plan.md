# Onboarding Questionnaire Implementation Plan

## Overview
This document outlines the complete implementation plan for an engaging, addictive post-login onboarding questionnaire that collects user data to pre-populate gift profiles.

## Goals
- Create an engaging, game-like experience for data collection
- Maximize completion rates through psychological engagement techniques
- Seamlessly integrate with existing profile creation system
- Collect high-quality data for personalized gift recommendations
- Make the process feel fun and addictive rather than tedious

## Technical Architecture

### File Structure
```
app/
├── (app)/
│   └── onboarding-questionnaire/
│       ├── _layout.tsx                 # Questionnaire layout wrapper
│       ├── index.tsx                   # Entry point & step router
│       ├── step-[stepNumber].tsx       # Individual step components (1-7)
│       └── complete.tsx                # Completion celebration page
│
components/
├── questionnaire/
│   ├── QuestionnaireLayout.tsx         # Common layout for all steps
│   ├── ProgressIndicator.tsx           # Animated progress bar
│   ├── StepTransition.tsx              # Page transition animations
│   ├── QuestionTypes/
│   │   ├── MultipleChoice.tsx          # Standard multiple choice
│   │   ├── BinaryChoice.tsx            # This-or-that questions
│   │   ├── VisualGrid.tsx              # Image/icon grid selection
│   │   ├── SliderInput.tsx             # Range/budget sliders
│   │   ├── SwipeCards.tsx              # Tinder-style swipe interface
│   │   └── TextInput.tsx               # Enhanced text input
│   ├── Celebrations/
│   │   ├── StepComplete.tsx            # Step completion animation
│   │   ├── ProgressCelebration.tsx     # Milestone celebrations
│   │   └── FinalCelebration.tsx        # Questionnaire completion
│   └── Gamification/
│       ├── BadgeSystem.tsx             # Achievement badges
│       ├── StreakCounter.tsx           # Completion streak
│       └── PersonalityInsights.tsx     # Real-time insights
│
contexts/
└── QuestionnaireContext.tsx            # State management for questionnaire data

types/
└── questionnaire.ts                    # TypeScript interfaces

utils/
├── questionnaireData.ts                # Question definitions and logic
├── profileMapping.ts                   # Maps questionnaire data to profile
└── gamificationLogic.ts               # Scoring and achievement logic
```

### Data Models

#### QuestionnaireData Interface
```typescript
interface QuestionnaireData {
  step1: {
    recipientName: string;
    relationship: string;
    giftOccasion: string[];
  };
  step2: {
    personalityType: string;
    lifestyle: string[];
    ageRange: string;
  };
  step3: {
    interestCategories: string[];
    hobbyLevel: 'casual' | 'serious' | 'professional';
  };
  step4: {
    stylePreferences: {
      colorPalette: string[];
      fashionStyle: string;
      homeDecorStyle: string;
    };
  };
  step5: {
    budgetRange: {
      min: number;
      max: number;
    };
    spendingComfort: 'conservative' | 'moderate' | 'generous';
  };
  step6: {
    giftHistory: {
      bestGiftGiven: string;
      worstGiftGiven: string;
      theirReactionStyle: string;
    };
  };
  step7: {
    specialConsiderations: string[];
    communicationStyle: string;
    surprisePreference: 'loves-surprises' | 'prefers-hints' | 'wants-to-know';
  };
}
```

#### Gamification Data
```typescript
interface GamificationState {
  currentStep: number;
  completedSteps: number[];
  badges: Badge[];
  insights: PersonalityInsight[];
  streak: number;
  engagementScore: number;
}

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt: Date;
}
```

## Step-by-Step Questionnaire Design

### Step 1: "Tell us about them" (Foundation)
**Objective**: Collect basic information and set the tone
**Engagement Strategy**: Personal connection, immediate value

**Questions**:
1. **Name Input** (Enhanced text input with celebration)
   - "What's their name?" 
   - Auto-capitalize, character celebration
   - Immediate personalization: "Great! Let's learn about [Name]"

2. **Relationship Selection** (Visual grid)
   - "How do you know [Name]?"
   - Icon-based selection with hover animations
   - Options: Partner, Family, Friend, Colleague, etc.

3. **Gift Occasions** (Multi-select with animations)
   - "When do you usually give [Name] gifts?"
   - Animated calendar icons
   - Options: Birthday, Anniversary, Holidays, Just Because, etc.

**Gamification Elements**:
- Welcome badge: "Profile Creator"
- Progress: 14% complete
- Insight: "You're building something special for [Name]!"

### Step 2: "What's [Name] like?" (Personality)
**Objective**: Understand personality and lifestyle
**Engagement Strategy**: Fun personality quiz vibes

**Questions**:
1. **Personality Type** (Binary choice with animations)
   - "Is [Name] more..." with sliding comparisons
   - Introvert ↔ Extrovert
   - Practical ↔ Creative
   - Traditional ↔ Trendy

2. **Lifestyle Tags** (Swipe cards interface)
   - "Swipe right on what describes [Name]"
   - Cards: Active, Homebody, Foodie, Tech-savvy, Artistic, etc.
   - Tinder-style swipe with satisfying animations

3. **Age Range** (Slider with personality insights)
   - "What's [Name]'s age range?"
   - Slider with real-time gift trend insights
   - "People in their 20s love experiences!"

**Gamification Elements**:
- Badge: "Personality Detective"
- Progress: 28% complete
- Insight: "[Name] sounds like someone who appreciates [insight]"

### Step 3: "What makes [Name] tick?" (Interests)
**Objective**: Deep dive into interests and hobbies
**Engagement Strategy**: Discovery and excitement

**Questions**:
1. **Interest Categories** (Visual grid with icons)
   - "Tap all that [Name] is into"
   - Animated icons: Sports, Music, Art, Tech, Fashion, etc.
   - Satisfying tap animations with sound

2. **Hobby Intensity** (Slider with personality)
   - "How serious is [Name] about their hobbies?"
   - Casual → Enthusiast → Expert
   - Dynamic descriptions change as you slide

3. **Discovery Question** (Open-ended with AI suggestions)
   - "What's something unique [Name] is passionate about?"
   - AI-powered suggestions based on previous answers
   - Optional but encouraged with "Unlock special insights" CTA

**Gamification Elements**:
- Badge: "Interest Explorer"
- Progress: 42% complete
- Insight: "We're seeing a pattern - [Name] loves [category]!"

### Step 4: "Style & Aesthetics" (Visual Preferences)
**Objective**: Understand visual and style preferences
**Engagement Strategy**: Beautiful visuals and instant feedback

**Questions**:
1. **Color Palette** (Visual color selection)
   - "Which colors does [Name] gravitate toward?"
   - Beautiful color swatches with names
   - Multi-select with visual feedback

2. **Fashion Style** (This-or-that with images)
   - "Which style is more [Name]?"
   - Image pairs: Classic vs Modern, Casual vs Formal, etc.
   - High-quality lifestyle images

3. **Home Aesthetic** (Mood board selection)
   - "If [Name] had their dream space..."
   - Pinterest-style mood boards
   - Minimalist, Cozy, Modern, Vintage, etc.

**Gamification Elements**:
- Badge: "Style Curator"
- Progress: 56% complete
- Insight: "[Name] has sophisticated taste in [style category]"

### Step 5: "Budget & Value" (Financial Comfort)
**Objective**: Understand spending comfort and value perception
**Engagement Strategy**: Non-judgmental, value-focused

**Questions**:
1. **Budget Range** (Dual slider with context)
   - "What's your typical gift budget for [Name]?"
   - Dual-handle slider with gift examples at each level
   - Context: "For birthdays" vs "For holidays"

2. **Value Philosophy** (Scenario-based choice)
   - "When giving gifts to [Name], you prefer..."
   - Scenarios with illustrations
   - "One meaningful gift" vs "Several smaller gifts"

3. **Spending Comfort** (Personality-based)
   - "Your gift-giving style is..."
   - Conservative, Thoughtful, Generous
   - Each with positive descriptions

**Gamification Elements**:
- Badge: "Thoughtful Giver"
- Progress: 70% complete
- Insight: "You value [value type] when choosing gifts"

### Step 6: "Gift History & Reactions" (Past Experience)
**Objective**: Learn from past successes and failures
**Engagement Strategy**: Storytelling and learning

**Questions**:
1. **Best Gift Memory** (Story input with prompts)
   - "Tell us about the best gift you gave [Name]"
   - Guided prompts: "What was it?" "How did they react?"
   - Optional photo upload

2. **Learning Experience** (Reflective choice)
   - "Was there ever a gift that didn't land well?"
   - Non-judgmental framing: "We all learn from experience"
   - What would you do differently?

3. **Reaction Style** (Personality insight)
   - "How does [Name] typically react to gifts?"
   - Animated character illustrations
   - Expressive, Grateful, Practical, Surprised

**Gamification Elements**:
- Badge: "Gift Historian"
- Progress: 84% complete
- Insight: "You're learning what makes [Name] happiest"

### Step 7: "Final Touches" (Special Considerations)
**Objective**: Capture unique details and preferences
**Engagement Strategy**: Completion excitement and anticipation

**Questions**:
1. **Special Considerations** (Multi-select with icons)
   - "Anything special to keep in mind for [Name]?"
   - Allergies, Dietary restrictions, Space limitations, etc.
   - Clear, helpful icons

2. **Surprise Preference** (Personality-based choice)
   - "How does [Name] feel about surprises?"
   - Illustrated scenarios
   - Loves surprises, Prefers hints, Wants to know

3. **Communication Style** (Relationship insight)
   - "How does [Name] usually express what they want?"
   - Direct, Subtle hints, Wishlist sharing, etc.

**Gamification Elements**:
- Badge: "Profile Master"
- Progress: 100% complete
- Major celebration: "You've created an amazing profile for [Name]!"

## Engagement & Gamification Features

### Progress & Motivation
1. **Animated Progress Bar**
   - Smooth animations between steps
   - Milestone celebrations at 25%, 50%, 75%
   - Visual progress with step indicators

2. **Badge System**
   - 7 core badges (one per step)
   - Special badges for engagement (Speed Demon, Thoughtful, etc.)
   - Beautiful badge animations and collection view

3. **Real-time Insights**
   - Personality insights generated as users progress
   - "Based on your answers, [Name] seems like..."
   - Building anticipation for final recommendations

4. **Streak & Engagement**
   - Track completion speed
   - Encourage momentum: "You're on a roll!"
   - Social proof: "Most people complete this in 5 minutes"

### Psychological Hooks
1. **Curiosity Gaps**
   - "We're building something special..."
   - "Based on this, we have some ideas..."
   - "You'll love what we found for [Name]"

2. **Immediate Gratification**
   - Instant feedback on each answer
   - Progressive insights building
   - Celebration animations

3. **Social Proof**
   - "People who chose this also selected..."
   - "This is a popular choice for [relationship]"
   - "Great choice! This helps us find perfect gifts"

4. **Personalization**
   - Use recipient's name throughout
   - Tailor questions based on relationship
   - Build on previous answers

## Technical Implementation Strategy

### Phase 1: Core Infrastructure (Week 1)
1. Create questionnaire routing and layout
2. Implement QuestionnaireContext for state management
3. Build basic question components
4. Set up progress tracking

### Phase 2: Question Components (Week 2)
1. Implement all question types with animations
2. Add validation and error handling
3. Create step transition animations
4. Build progress indicator

### Phase 3: Gamification (Week 3)
1. Implement badge system
2. Add celebration animations
3. Create insight generation logic
4. Build completion flow

### Phase 4: Integration (Week 4)
1. Map questionnaire data to profile structure
2. Integrate with existing profile creation
3. Add analytics tracking
4. Implement A/B testing framework

### Phase 5: Polish & Optimization (Week 5)
1. Performance optimization
2. Accessibility improvements
3. Error handling and edge cases
4. User testing and iteration

## Data Flow & Integration

### Questionnaire → Profile Mapping
```typescript
const mapQuestionnaireToProfile = (data: QuestionnaireData): Partial<ProfileFormData> => {
  return {
    name: data.step1.recipientName,
    relationship: data.step1.relationship,
    interests: extractInterests(data.step3, data.step2),
    preferences: {
      favoriteColor: data.step4.stylePreferences.colorPalette[0],
      preferredStyle: data.step4.stylePreferences.fashionStyle,
      budgetMin: data.step5.budgetRange.min,
      budgetMax: data.step5.budgetRange.max,
    },
    // ... additional mappings
  };
};
```

### Analytics Tracking
- Step completion rates
- Time spent per step
- Drop-off points
- Answer patterns
- Badge unlock rates

## Success Metrics

### Engagement Metrics
- Questionnaire completion rate (target: >85%)
- Average time to complete (target: 3-5 minutes)
- Step-by-step drop-off rates
- Badge collection rates

### Quality Metrics
- Profile completeness after questionnaire
- User satisfaction with pre-filled data
- Subsequent profile editing rates
- Gift recommendation accuracy

### Business Metrics
- User activation rate (completing first profile)
- Time to first gift recommendation
- User retention after onboarding
- Profile creation conversion rate

## Future Enhancements

### Advanced Features
1. **AI-Powered Insights**
   - Machine learning for better question routing
   - Predictive text for open-ended questions
   - Personality analysis integration

2. **Social Features**
   - Share questionnaire with friends for input
   - Collaborative profile building
   - Gift idea voting

3. **Adaptive Questioning**
   - Dynamic question routing based on answers
   - Shorter flows for certain relationships
   - Advanced flows for gift enthusiasts

4. **Rich Media**
   - Video questions for engagement
   - Audio responses for accessibility
   - AR/VR style preference selection

## Implementation Checklist

### Pre-Development
- [ ] Finalize question content and copy
- [ ] Design visual assets and icons
- [ ] Create animation specifications
- [ ] Set up analytics tracking plan

### Development Phases
- [ ] Phase 1: Core Infrastructure
- [ ] Phase 2: Question Components
- [ ] Phase 3: Gamification
- [ ] Phase 4: Integration
- [ ] Phase 5: Polish & Optimization

### Testing & Launch
- [ ] Unit testing for all components
- [ ] Integration testing with existing profile system
- [ ] User acceptance testing
- [ ] A/B testing setup
- [ ] Performance testing
- [ ] Accessibility testing
- [ ] Soft launch with analytics monitoring
- [ ] Full launch with success metrics tracking

This plan provides a comprehensive roadmap for creating an engaging, addictive onboarding questionnaire that will significantly improve user activation and profile quality while maintaining the fun, personal nature of gift-giving.

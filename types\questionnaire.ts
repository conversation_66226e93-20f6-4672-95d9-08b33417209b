// Questionnaire TypeScript Interfaces and Types

export interface QuestionnaireStep1Data {
  recipientName: string;
  relationship: string;
  giftOccasions: string[];
}

export interface QuestionnaireStep2Data {
  personalityType: {
    introvertExtrovert: 'introvert' | 'extrovert' | 'ambivert';
    practicalCreative: 'practical' | 'creative' | 'balanced';
    traditionalTrendy: 'traditional' | 'trendy' | 'eclectic';
  };
  lifestyleTags: string[];
  ageRange: string;
}

export interface QuestionnaireStep3Data {
  interestCategories: string[];
  hobbyLevel: 'casual' | 'serious' | 'professional';
  uniquePassion?: string;
}

export interface QuestionnaireStep4Data {
  stylePreferences: {
    colorPalette: string[];
    fashionStyle: string;
    homeDecorStyle: string;
  };
}

export interface QuestionnaireStep5Data {
  budgetRange: {
    min: number;
    max: number;
  };
  spendingComfort: 'conservative' | 'moderate' | 'generous';
  valuePhilosophy: 'one-meaningful' | 'several-smaller' | 'experience-focused';
}

export interface QuestionnaireStep6Data {
  giftHistory: {
    bestGiftGiven?: string;
    worstGiftGiven?: string;
    theirReactionStyle: string;
  };
}

export interface QuestionnaireStep7Data {
  specialConsiderations: string[];
  communicationStyle: string;
  surprisePreference: 'loves-surprises' | 'prefers-hints' | 'wants-to-know';
}

export interface QuestionnaireData {
  step1?: QuestionnaireStep1Data;
  step2?: QuestionnaireStep2Data;
  step3?: QuestionnaireStep3Data;
  step4?: QuestionnaireStep4Data;
  step5?: QuestionnaireStep5Data;
  step6?: QuestionnaireStep6Data;
  step7?: QuestionnaireStep7Data;
  completedAt?: Date;
  startedAt?: Date;
}

// Gamification Types
export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt: Date;
  category: 'progress' | 'engagement' | 'completion' | 'special';
}

export interface PersonalityInsight {
  id: string;
  message: string;
  category: 'personality' | 'interests' | 'style' | 'relationship';
  confidence: number; // 0-1
  basedOnSteps: number[];
}

export interface GamificationState {
  currentStep: number;
  completedSteps: number[];
  badges: Badge[];
  insights: PersonalityInsight[];
  streak: number;
  engagementScore: number;
  timeSpent: number; // in seconds
  startTime?: Date;
}

// Question Component Types
export interface BaseQuestionProps {
  title: string;
  subtitle?: string;
  isRequired?: boolean;
  onAnswer: (answer: any) => void;
  currentAnswer?: any;
  onNext?: () => void;
  onBack?: () => void;
  isValid?: boolean;
}

export interface MultipleChoiceOption {
  id: string;
  label: string;
  description?: string;
  icon?: string;
  image?: string;
}

export interface MultipleChoiceProps extends BaseQuestionProps {
  options: MultipleChoiceOption[];
  allowMultiple?: boolean;
  maxSelections?: number;
  minSelections?: number;
}

export interface BinaryChoiceProps extends BaseQuestionProps {
  leftOption: {
    label: string;
    description?: string;
    icon?: string;
  };
  rightOption: {
    label: string;
    description?: string;
    icon?: string;
  };
  showSlider?: boolean;
}

export interface VisualGridProps extends BaseQuestionProps {
  items: {
    id: string;
    label: string;
    icon?: string;
    image?: string;
    color?: string;
  }[];
  columns?: number;
  allowMultiple?: boolean;
  maxSelections?: number;
}

export interface SliderInputProps extends BaseQuestionProps {
  min: number;
  max: number;
  step?: number;
  showLabels?: boolean;
  leftLabel?: string;
  rightLabel?: string;
  formatValue?: (value: number) => string;
  isDual?: boolean; // For range sliders
}

export interface SwipeCardData {
  id: string;
  label: string;
  description?: string;
  image?: string;
  category?: string;
}

export interface SwipeCardsProps extends BaseQuestionProps {
  cards: SwipeCardData[];
  onSwipeRight: (card: SwipeCardData) => void;
  onSwipeLeft: (card: SwipeCardData) => void;
  maxSelections?: number;
}

export interface TextInputProps extends BaseQuestionProps {
  placeholder?: string;
  multiline?: boolean;
  maxLength?: number;
  suggestions?: string[];
  showSuggestions?: boolean;
}

// Navigation and Flow Types
export interface QuestionnaireStep {
  id: number;
  title: string;
  subtitle: string;
  component: React.ComponentType<any>;
  isRequired: boolean;
  estimatedTime: number; // in seconds
  badgeId?: string;
}

export interface QuestionnaireProgress {
  currentStep: number;
  totalSteps: number;
  completedSteps: number[];
  percentage: number;
  estimatedTimeRemaining: number;
}

// Context Types
export interface QuestionnaireContextType {
  data: QuestionnaireData;
  gamification: GamificationState;
  progress: QuestionnaireProgress;
  updateStepData: (step: number, data: any) => void;
  completeStep: (step: number) => void;
  goToStep: (step: number) => void;
  addBadge: (badge: Badge) => void;
  addInsight: (insight: PersonalityInsight) => void;
  resetQuestionnaire: () => void;
  saveProgress: () => Promise<void>;
  loadProgress: () => Promise<void>;
}

// Animation and UI Types
export interface AnimationConfig {
  duration: number;
  delay?: number;
  easing?: string;
  type?: 'spring' | 'timing';
}

export interface CelebrationConfig {
  type: 'step-complete' | 'milestone' | 'final';
  title: string;
  message: string;
  badge?: Badge;
  insight?: PersonalityInsight;
  animation: AnimationConfig;
}

// Analytics Types
export interface QuestionnaireAnalytics {
  sessionId: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  completedSteps: number[];
  dropOffStep?: number;
  timePerStep: Record<number, number>;
  answers: Record<number, any>;
  badges: string[];
  engagementEvents: AnalyticsEvent[];
}

export interface AnalyticsEvent {
  type: 'step_start' | 'step_complete' | 'answer_change' | 'badge_unlock' | 'insight_view' | 'back_navigation';
  timestamp: Date;
  step?: number;
  data?: any;
}

// Utility Types
export type QuestionType = 'multiple-choice' | 'binary-choice' | 'visual-grid' | 'slider' | 'swipe-cards' | 'text-input';

export type RelationshipType = 'partner' | 'family' | 'friend' | 'colleague' | 'other';

export type GiftOccasion = 'birthday' | 'anniversary' | 'holiday' | 'graduation' | 'promotion' | 'just-because' | 'apology' | 'thank-you';

export type InterestCategory = 'sports' | 'music' | 'art' | 'technology' | 'fashion' | 'cooking' | 'travel' | 'reading' | 'gaming' | 'fitness' | 'nature' | 'crafts';

export type StyleCategory = 'minimalist' | 'bohemian' | 'classic' | 'modern' | 'vintage' | 'eclectic' | 'rustic' | 'glamorous';

export type ColorPalette = 'warm' | 'cool' | 'neutral' | 'bold' | 'pastel' | 'monochrome' | 'earth-tones' | 'jewel-tones';

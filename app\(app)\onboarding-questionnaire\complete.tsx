import React, { useEffect } from 'react';
import { View, Text, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import Animated, { FadeInDown, FadeInUp, ZoomIn } from 'react-native-reanimated';
import Button from '@/components/ui/Button';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { useColorScheme } from 'nativewind';
import * as Haptics from 'expo-haptics';

export default function QuestionnaireComplete() {
  const router = useRouter();
  const { data, gamification, resetQuestionnaire } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const themedColors = {
    background: isDark ? '#0F0F0F' : '#FFFFFF',
    text: isDark ? '#FFFFFF' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    accent: '#E5355F',
    success: '#10B981',
    card: isDark ? '#1F2937' : '#F9FAFB',
  };

  const recipientName = data.step1?.recipientName || 'them';

  useEffect(() => {
    // Celebration haptics
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  }, []);

  const handleCreateProfile = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Map questionnaire data to profile format
    const { mapQuestionnaireToProfile } = await import('../../../utils/profileMapping');
    const profileData = mapQuestionnaireToProfile(data);

    // Store the mapped data for the profile creation form
    // For now, redirect to profile creation with the data available in context
    router.replace('/profiles/add');
  };

  const handleViewInsights = () => {
    // TODO: Show personality insights page
    console.log('Questionnaire data:', data);
    console.log('Gamification data:', gamification);
  };

  return (
    <SafeAreaView
      style={{ backgroundColor: themedColors.background }}
      className="flex-1"
    >
      <View className="flex-1 px-6 py-8">
        {/* Celebration Header */}
        <Animated.View
          entering={ZoomIn.duration(800).delay(200)}
          className="items-center mb-8"
        >
          <Text className="text-6xl mb-4">🎉</Text>
          <Text
            style={{ color: themedColors.text }}
            className="text-3xl font-bold text-center mb-2"
          >
            Amazing!
          </Text>
          <Text
            style={{ color: themedColors.success }}
            className="text-xl font-semibold text-center"
          >
            You've created the perfect profile for {recipientName}!
          </Text>
        </Animated.View>

        {/* Achievement Summary */}
        <Animated.View
          entering={FadeInUp.duration(800).delay(400)}
          className="mb-8"
        >
          <View
            className="p-6 rounded-2xl"
            style={{ backgroundColor: themedColors.card }}
          >
            <Text
              style={{ color: themedColors.text }}
              className="text-lg font-bold mb-4 text-center"
            >
              Your Achievements
            </Text>

            {/* Badges */}
            <View className="flex-row flex-wrap justify-center mb-4">
              {gamification.badges.map((badge, index) => (
                <Animated.View
                  key={badge.id}
                  entering={FadeInDown.duration(600).delay(600 + index * 100)}
                  className="m-2 items-center"
                >
                  <Text className="text-2xl mb-1">{badge.icon}</Text>
                  <Text
                    style={{ color: themedColors.textSecondary }}
                    className="text-xs text-center"
                  >
                    {badge.name}
                  </Text>
                </Animated.View>
              ))}
            </View>

            {/* Stats */}
            <View className="flex-row justify-around">
              <View className="items-center">
                <Text
                  style={{ color: themedColors.accent }}
                  className="text-2xl font-bold"
                >
                  {gamification.completedSteps.length}
                </Text>
                <Text
                  style={{ color: themedColors.textSecondary }}
                  className="text-sm"
                >
                  Steps Completed
                </Text>
              </View>

              <View className="items-center">
                <Text
                  style={{ color: themedColors.accent }}
                  className="text-2xl font-bold"
                >
                  {gamification.badges.length}
                </Text>
                <Text
                  style={{ color: themedColors.textSecondary }}
                  className="text-sm"
                >
                  Badges Earned
                </Text>
              </View>

              <View className="items-center">
                <Text
                  style={{ color: themedColors.accent }}
                  className="text-2xl font-bold"
                >
                  {Math.floor(gamification.timeSpent / 60)}m
                </Text>
                <Text
                  style={{ color: themedColors.textSecondary }}
                  className="text-sm"
                >
                  Time Invested
                </Text>
              </View>
            </View>
          </View>
        </Animated.View>

        {/* What's Next */}
        <Animated.View
          entering={FadeInDown.duration(800).delay(800)}
          className="flex-1 justify-center"
        >
          <Text
            style={{ color: themedColors.text }}
            className="text-xl font-bold mb-4 text-center"
          >
            What's Next?
          </Text>

          <Text
            style={{ color: themedColors.textSecondary }}
            className="text-base text-center mb-8 leading-relaxed"
          >
            We'll use everything you've shared to create {recipientName}'s profile and start generating personalized gift recommendations just for them!
          </Text>
        </Animated.View>

        {/* Action Buttons */}
        <Animated.View
          entering={FadeInDown.duration(800).delay(1000)}
          className="space-y-4"
        >
          <Button
            title="Create Profile & Get Recommendations"
            onPress={handleCreateProfile}
            variant="primary"
            className="w-full py-4 rounded-xl shadow-lg"
            style={{ backgroundColor: themedColors.accent }}
          />

          <Button
            title="View Personality Insights"
            onPress={handleViewInsights}
            variant="secondary"
            className="w-full py-4 rounded-xl"
          />
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}

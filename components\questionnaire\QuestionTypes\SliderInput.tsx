import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
// Using a simple custom slider implementation for now
// import { Slider } from '@react-native-community/slider';
import { useColorScheme } from 'nativewind';
import { SliderInputProps } from '../../../types/questionnaire';

export default function SliderInput({
  title,
  subtitle,
  min,
  max,
  step = 1,
  showLabels = true,
  leftLabel,
  rightLabel,
  formatValue,
  isDual = false,
  onAnswer,
  currentAnswer,
  isRequired = false,
}: SliderInputProps) {
  const [value, setValue] = useState(
    isDual 
      ? (Array.isArray(currentAnswer) ? currentAnswer : [min, max])
      : (typeof currentAnswer === 'number' ? currentAnswer : min)
  );
  
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const celebrationScale = useSharedValue(1);

  const themedColors = {
    background: isDark ? '#1F2937' : '#F9FAFB',
    text: isDark ? '#FFFFFF' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    accent: '#E5355F',
    track: isDark ? '#374151' : '#E5E7EB',
  };

  useEffect(() => {
    onAnswer(value);
  }, [value, onAnswer]);

  const handleValueChange = (newValue: number | number[]) => {
    setValue(newValue);
    
    // Celebration animation
    celebrationScale.value = withSpring(1.05, {}, () => {
      celebrationScale.value = withSpring(1);
    });
  };

  const formatDisplayValue = (val: number) => {
    if (formatValue) {
      return formatValue(val);
    }
    return val.toString();
  };

  const celebrationStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: celebrationScale.value }],
    };
  });

  const renderSingleSlider = () => {
    const currentValue = typeof value === 'number' ? value : min;
    const percentage = ((currentValue - min) / (max - min)) * 100;

    return (
      <View className="mb-6">
        {/* Custom Slider Track */}
        <View className="relative h-2 rounded-full mb-4" style={{ backgroundColor: themedColors.track }}>
          <View
            className="absolute h-2 rounded-full"
            style={{
              backgroundColor: themedColors.accent,
              width: `${percentage}%`
            }}
          />
          <View
            className="absolute w-6 h-6 rounded-full border-2 border-white shadow-lg"
            style={{
              backgroundColor: themedColors.accent,
              left: `${Math.max(0, Math.min(100, percentage))}%`,
              top: -8,
              marginLeft: -12,
            }}
          />
        </View>

        {/* Value Display */}
        <View className="mt-4 items-center">
          <Animated.View style={celebrationStyle}>
            <Text
              style={{ color: themedColors.accent }}
              className="text-2xl font-bold"
            >
              {formatDisplayValue(currentValue)}
            </Text>
          </Animated.View>
        </View>
      </View>
    );
  };

  const renderDualSlider = () => {
    const [minVal, maxVal] = Array.isArray(value) ? value : [min, max];

    return (
      <View className="mb-6">
        {/* For now, show a simple range display */}
        <View className="p-4 rounded-xl border-2" style={{
          backgroundColor: themedColors.background,
          borderColor: themedColors.accent
        }}>
          <Text style={{ color: themedColors.text }} className="text-center text-lg font-semibold mb-2">
            Budget Range
          </Text>
          <Animated.View style={celebrationStyle}>
            <Text
              style={{ color: themedColors.accent }}
              className="text-xl font-bold text-center"
            >
              {formatDisplayValue(minVal)} - {formatDisplayValue(maxVal)}
            </Text>
          </Animated.View>
        </View>
      </View>
    );
  };

  return (
    <View className="w-full">
      {/* Title */}
      <Animated.View style={celebrationStyle} className="mb-6">
        <Text 
          style={{ color: themedColors.text }}
          className="text-xl font-bold mb-2"
        >
          {title}
          {isRequired && <Text style={{ color: themedColors.accent }}> *</Text>}
        </Text>
        {subtitle && (
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-base leading-relaxed"
          >
            {subtitle}
          </Text>
        )}
      </Animated.View>

      {/* Slider */}
      {isDual ? renderDualSlider() : renderSingleSlider()}

      {/* Labels */}
      {showLabels && (leftLabel || rightLabel) && (
        <View className="flex-row justify-between mb-4">
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-sm"
          >
            {leftLabel || formatDisplayValue(min)}
          </Text>
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-sm"
          >
            {rightLabel || formatDisplayValue(max)}
          </Text>
        </View>
      )}

      {/* Encouragement Message */}
      <Animated.View 
        entering={withSpring(1)}
        className="mt-2"
      >
        <Text 
          style={{ color: themedColors.accent }}
          className="text-sm font-medium text-center"
        >
          {isDual ? "Perfect range! 💰" : "Great choice! 👍"}
        </Text>
      </Animated.View>
    </View>
  );
}

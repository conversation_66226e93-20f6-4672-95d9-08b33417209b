import React, { useState, useEffect } from 'react';
import { View, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import QuestionnaireLayout from '../../../components/questionnaire/QuestionnaireLayout';
import TextInput from '../../../components/questionnaire/QuestionTypes/TextInput';
import VisualGrid from '../../../components/questionnaire/QuestionTypes/VisualGrid';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { QuestionnaireStep1Data } from '../../../types/questionnaire';

const relationshipOptions = [
  { id: 'partner', label: 'Partner', icon: 'heart' },
  { id: 'spouse', label: 'Spouse', icon: 'heart' },
  { id: 'girlfriend', label: 'Girlfriend', icon: 'heart' },
  { id: 'boyfriend', label: 'Boyfriend', icon: 'heart' },
  { id: 'mother', label: 'Mother', icon: 'user' },
  { id: 'father', label: 'Father', icon: 'user' },
  { id: 'sibling', label: 'Sibling', icon: 'users' },
  { id: 'child', label: 'Child', icon: 'baby' },
  { id: 'friend', label: 'Friend', icon: 'smile' },
  { id: 'colleague', label: 'Colleague', icon: 'briefcase' },
  { id: 'family', label: 'Family Member', icon: 'home' },
  { id: 'other', label: 'Other', icon: 'more-horizontal' },
];

const giftOccasionOptions = [
  { id: 'birthday', label: 'Birthday', icon: 'gift' },
  { id: 'anniversary', label: 'Anniversary', icon: 'heart' },
  { id: 'holiday', label: 'Holidays', icon: 'star' },
  { id: 'graduation', label: 'Graduation', icon: 'award' },
  { id: 'promotion', label: 'Promotion', icon: 'trending-up' },
  { id: 'just-because', label: 'Just Because', icon: 'smile' },
  { id: 'apology', label: 'Apology', icon: 'heart' },
  { id: 'thank-you', label: 'Thank You', icon: 'thumbs-up' },
];

export default function Step1() {
  const router = useRouter();
  const { data, updateStepData, completeStep, addBadge } = useQuestionnaire();

  const [currentStep, setCurrentStep] = useState(1); // 1: name, 2: relationship, 3: occasions
  const [stepData, setStepData] = useState<QuestionnaireStep1Data>({
    recipientName: data.step1?.recipientName || '',
    relationship: data.step1?.relationship || '',
    giftOccasions: data.step1?.giftOccasions || [],
  });

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return stepData.recipientName.trim().length > 0;
      case 2:
        return stepData.relationship.length > 0;
      case 3:
        return stepData.giftOccasions.length > 0;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete step and move to next questionnaire step
      updateStepData(1, stepData);
      completeStep(1);

      // Award first badge
      addBadge({
        id: 'profile-creator',
        name: 'Profile Creator',
        description: 'Started creating a gift profile',
        icon: '🎯',
        category: 'progress',
        unlockedAt: new Date(),
      });

      router.push('/(app)/onboarding-questionnaire/step-2');
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const updateAnswer = (field: keyof QuestionnaireStep1Data, value: any) => {
    setStepData(prev => ({ ...prev, [field]: value }));
  };

  const renderCurrentQuestion = () => {
    switch (currentStep) {
      case 1:
        return (
          <TextInput
            title="What's their name?"
            subtitle="This helps us personalize everything for them"
            placeholder="Enter their name"
            onAnswer={(value) => updateAnswer('recipientName', value)}
            currentAnswer={stepData.recipientName}
            isRequired={true}
            maxLength={50}
          />
        );

      case 2:
        return (
          <VisualGrid
            title={`How do you know ${stepData.recipientName || 'them'}?`}
            subtitle="This helps us understand your relationship dynamic"
            items={relationshipOptions}
            columns={2}
            allowMultiple={false}
            onAnswer={(value) => updateAnswer('relationship', value)}
            currentAnswer={stepData.relationship}
            isRequired={true}
          />
        );

      case 3:
        return (
          <VisualGrid
            title={`When do you usually give ${stepData.recipientName || 'them'} gifts?`}
            subtitle="Select all occasions that apply"
            items={giftOccasionOptions}
            columns={2}
            allowMultiple={true}
            maxSelections={5}
            onAnswer={(value) => updateAnswer('giftOccasions', value)}
            currentAnswer={stepData.giftOccasions}
            isRequired={true}
          />
        );

      default:
        return null;
    }
  };

  const getNextButtonText = () => {
    if (currentStep < 3) return "Continue";
    return "Next Step";
  };

  return (
    <QuestionnaireLayout
      title="Tell us about them"
      subtitle="Let's start with the basics"
      stepNumber={1}
      onNext={handleNext}
      onBack={handleBack}
      nextButtonText={getNextButtonText()}
      isNextDisabled={!isStepValid()}
      showBackButton={true}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        {renderCurrentQuestion()}
      </ScrollView>
    </QuestionnaireLayout>
  );
}

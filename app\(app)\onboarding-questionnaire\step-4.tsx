import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import QuestionnaireLayout from '../../../components/questionnaire/QuestionnaireLayout';
import VisualGrid from '../../../components/questionnaire/QuestionTypes/VisualGrid';
import ColorPalette from '../../../components/questionnaire/QuestionTypes/ColorPalette';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { QuestionnaireStep4Data } from '../../../types/questionnaire';
import { useColorScheme } from 'nativewind';

const colorPalettes = [
  {
    id: 'warm',
    name: 'Warm & Cozy',
    colors: ['#FF6B6B', '#FFE66D', '#FF8E53'],
    description: 'Reds, oranges, yellows'
  },
  {
    id: 'cool',
    name: 'Cool & Calm',
    colors: ['#4ECDC4', '#45B7D1', '#96CEB4'],
    description: 'Blues, greens, purples'
  },
  {
    id: 'neutral',
    name: 'Neutral & Elegant',
    colors: ['#F7F1E3', '#E2D5C7', '#B8A082'],
    description: 'Beiges, grays, whites'
  },
  {
    id: 'bold',
    name: 'Bold & Vibrant',
    colors: ['#E74C3C', '#9B59B6', '#F39C12'],
    description: 'Bright, eye-catching colors'
  },
  {
    id: 'pastel',
    name: 'Soft & Pastel',
    colors: ['#FFB3BA', '#BAFFC9', '#BAE1FF'],
    description: 'Gentle, muted tones'
  },
  {
    id: 'monochrome',
    name: 'Classic Monochrome',
    colors: ['#2C3E50', '#7F8C8D', '#ECF0F1'],
    description: 'Black, white, gray'
  },
];

const fashionStyles = [
  { id: 'classic', label: 'Classic & Timeless', icon: 'award', image: require('@/assets/images/onboarding1.png') },
  { id: 'modern', label: 'Modern & Trendy', icon: 'trending-up', image: require('@/assets/images/onboarding2.png') },
  { id: 'casual', label: 'Casual & Comfortable', icon: 'smile', image: require('@/assets/images/onboarding3.png') },
  { id: 'elegant', label: 'Elegant & Sophisticated', icon: 'star', image: require('@/assets/images/onboarding1.png') },
  { id: 'bohemian', label: 'Bohemian & Free-spirited', icon: 'sun', image: require('@/assets/images/onboarding2.png') },
  { id: 'minimalist', label: 'Minimalist & Clean', icon: 'minus-circle', image: require('@/assets/images/onboarding3.png') },
];

const homeStyles = [
  { id: 'modern', label: 'Modern & Sleek', icon: 'square', image: require('@/assets/images/onboarding1.png') },
  { id: 'cozy', label: 'Cozy & Warm', icon: 'home', image: require('@/assets/images/onboarding2.png') },
  { id: 'minimalist', label: 'Minimalist & Clean', icon: 'minus-circle', image: require('@/assets/images/onboarding3.png') },
  { id: 'vintage', label: 'Vintage & Retro', icon: 'clock', image: require('@/assets/images/onboarding1.png') },
  { id: 'rustic', label: 'Rustic & Natural', icon: 'tree', image: require('@/assets/images/onboarding2.png') },
  { id: 'glamorous', label: 'Glamorous & Luxe', icon: 'star', image: require('@/assets/images/onboarding3.png') },
];

export default function Step4() {
  const router = useRouter();
  const { data, updateStepData, completeStep, addBadge } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [currentStep, setCurrentStep] = useState(1); // 1: colors, 2: fashion, 3: home
  const [stepData, setStepData] = useState<QuestionnaireStep4Data>({
    stylePreferences: {
      colorPalette: data.step4?.stylePreferences?.colorPalette || [],
      fashionStyle: data.step4?.stylePreferences?.fashionStyle || '',
      homeDecorStyle: data.step4?.stylePreferences?.homeDecorStyle || '',
    },
  });

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return stepData.stylePreferences.colorPalette.length > 0;
      case 2:
        return stepData.stylePreferences.fashionStyle.length > 0;
      case 3:
        return stepData.stylePreferences.homeDecorStyle.length > 0;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete step and move to next questionnaire step
      updateStepData(4, stepData);
      completeStep(4);

      // Award style curator badge
      addBadge({
        id: 'style-curator',
        name: 'Style Curator',
        description: 'Discovered their aesthetic preferences',
        icon: '🎨',
        category: 'progress',
        unlockedAt: new Date(),
      });

      router.push('/(app)/onboarding-questionnaire/step-5');
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const updateStylePreference = (field: keyof QuestionnaireStep4Data['stylePreferences'], value: any) => {
    setStepData(prev => ({
      ...prev,
      stylePreferences: {
        ...prev.stylePreferences,
        [field]: value,
      },
    }));
  };

  const recipientName = data.step1?.recipientName || 'them';

  const renderCurrentQuestion = () => {
    switch (currentStep) {
      case 1:
        return (
          <ColorPalette
            title={`What colors does ${recipientName} gravitate toward?`}
            subtitle="Select the color palettes that match their style"
            colors={colorPalettes}
            allowMultiple={true}
            maxSelections={3}
            onAnswer={(value) => updateStylePreference('colorPalette', value)}
            currentAnswer={stepData.stylePreferences.colorPalette}
            isRequired={true}
          />
        );

      case 2:
        return (
          <VisualGrid
            title={`How would you describe ${recipientName}'s fashion style?`}
            subtitle="Choose the style that best represents them"
            items={fashionStyles}
            columns={2}
            allowMultiple={false}
            onAnswer={(value) => updateStylePreference('fashionStyle', value)}
            currentAnswer={stepData.stylePreferences.fashionStyle}
            isRequired={true}
          />
        );

      case 3:
        return (
          <VisualGrid
            title={`What's ${recipientName}'s ideal home aesthetic?`}
            subtitle="This helps us suggest home decor and lifestyle gifts"
            items={homeStyles}
            columns={2}
            allowMultiple={false}
            onAnswer={(value) => updateStylePreference('homeDecorStyle', value)}
            currentAnswer={stepData.stylePreferences.homeDecorStyle}
            isRequired={true}
          />
        );

      default:
        return null;
    }
  };

  const getNextButtonText = () => {
    if (currentStep < 3) return "Continue";
    return "Next Step";
  };

  return (
    <QuestionnaireLayout
      title={`${recipientName}'s Style & Aesthetics`}
      subtitle="Understanding their visual preferences"
      stepNumber={4}
      onNext={handleNext}
      onBack={handleBack}
      nextButtonText={getNextButtonText()}
      isNextDisabled={!isStepValid()}
      showBackButton={true}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        {renderCurrentQuestion()}

        {/* Progress indicator for sub-steps */}
        <View className="mt-6 flex-row justify-center">
          {[1, 2, 3].map((step) => (
            <View
              key={step}
              className={`w-2 h-2 rounded-full mx-1 ${
                step === currentStep
                  ? 'bg-red-500'
                  : step < currentStep
                    ? 'bg-green-500'
                    : isDark
                      ? 'bg-gray-600'
                      : 'bg-gray-300'
              }`}
            />
          ))}
        </View>
      </ScrollView>
    </QuestionnaireLayout>
  );
}

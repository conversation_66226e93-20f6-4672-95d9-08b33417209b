import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import QuestionnaireLayout from '../../../components/questionnaire/QuestionnaireLayout';
import VisualGrid from '../../../components/questionnaire/QuestionTypes/VisualGrid';
import TextInput from '../../../components/questionnaire/QuestionTypes/TextInput';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { QuestionnaireStep3Data } from '../../../types/questionnaire';
import { useColorScheme } from 'nativewind';

const interestOptions = [
  { id: 'sports', label: 'Sports & Fitness', icon: 'activity', image: require('@/assets/images/onboarding1.png') },
  { id: 'music', label: 'Music', icon: 'music', image: require('@/assets/images/onboarding2.png') },
  { id: 'art', label: 'Art & Design', icon: 'palette', image: require('@/assets/images/onboarding3.png') },
  { id: 'technology', label: 'Technology', icon: 'smartphone', image: require('@/assets/images/onboarding1.png') },
  { id: 'fashion', label: 'Fashion & Style', icon: 'shopping-bag', image: require('@/assets/images/onboarding2.png') },
  { id: 'cooking', label: 'Cooking & Food', icon: 'coffee', image: require('@/assets/images/onboarding3.png') },
  { id: 'travel', label: 'Travel', icon: 'map-pin', image: require('@/assets/images/onboarding1.png') },
  { id: 'reading', label: 'Reading & Books', icon: 'book', image: require('@/assets/images/onboarding2.png') },
  { id: 'gaming', label: 'Gaming', icon: 'gamepad-2', image: require('@/assets/images/onboarding3.png') },
  { id: 'nature', label: 'Nature & Outdoors', icon: 'sun', image: require('@/assets/images/onboarding1.png') },
  { id: 'crafts', label: 'Crafts & DIY', icon: 'scissors', image: require('@/assets/images/onboarding2.png') },
  { id: 'wellness', label: 'Health & Wellness', icon: 'heart', image: require('@/assets/images/onboarding3.png') },
];

const hobbyLevelOptions = [
  { id: 'casual', label: 'Casual Interest', icon: 'smile' },
  { id: 'serious', label: 'Serious Hobbyist', icon: 'star' },
  { id: 'professional', label: 'Professional Level', icon: 'award' },
];

export default function Step3() {
  const router = useRouter();
  const { data, updateStepData, completeStep, addBadge } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [currentStep, setCurrentStep] = useState(1); // 1: interests, 2: hobby level, 3: unique passion
  const [stepData, setStepData] = useState<QuestionnaireStep3Data>({
    interestCategories: data.step3?.interestCategories || [],
    hobbyLevel: data.step3?.hobbyLevel || 'casual',
    uniquePassion: data.step3?.uniquePassion || '',
  });

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return stepData.interestCategories.length > 0;
      case 2:
        return stepData.hobbyLevel.length > 0;
      case 3:
        return true; // Optional step
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete step and move to next questionnaire step
      updateStepData(3, stepData);
      completeStep(3);

      // Award interest explorer badge
      addBadge({
        id: 'interest-explorer',
        name: 'Interest Explorer',
        description: 'Discovered their passions and interests',
        icon: '🔍',
        category: 'progress',
        unlockedAt: new Date(),
      });

      router.push('/(app)/onboarding-questionnaire/step-4');
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const updateAnswer = (field: keyof QuestionnaireStep3Data, value: any) => {
    setStepData(prev => ({ ...prev, [field]: value }));
  };

  const recipientName = data.step1?.recipientName || 'them';

  const renderCurrentQuestion = () => {
    switch (currentStep) {
      case 1:
        return (
          <VisualGrid
            title={`What is ${recipientName} passionate about?`}
            subtitle="Select all interests that describe them"
            items={interestOptions}
            columns={2}
            allowMultiple={true}
            maxSelections={6}
            onAnswer={(value) => updateAnswer('interestCategories', value)}
            currentAnswer={stepData.interestCategories}
            isRequired={true}
          />
        );

      case 2:
        return (
          <VisualGrid
            title={`How serious is ${recipientName} about their hobbies?`}
            subtitle="This helps us find the right level of gifts"
            items={hobbyLevelOptions}
            columns={1}
            allowMultiple={false}
            onAnswer={(value) => updateAnswer('hobbyLevel', value)}
            currentAnswer={stepData.hobbyLevel}
            isRequired={true}
          />
        );

      case 3:
        return (
          <TextInput
            title={`What's something unique ${recipientName} is passionate about?`}
            subtitle="Tell us about any special interests or hobbies we might have missed (optional)"
            placeholder="e.g., vintage vinyl records, urban gardening, astronomy..."
            multiline={true}
            maxLength={200}
            onAnswer={(value) => updateAnswer('uniquePassion', value)}
            currentAnswer={stepData.uniquePassion}
            isRequired={false}
            showSuggestions={true}
            suggestions={[
              'Vintage collecting',
              'Urban gardening',
              'Astronomy',
              'Board games',
              'Meditation',
              'Photography',
            ]}
          />
        );

      default:
        return null;
    }
  };

  const getNextButtonText = () => {
    if (currentStep < 3) return "Continue";
    return "Next Step";
  };

  return (
    <QuestionnaireLayout
      title={`What makes ${recipientName} tick?`}
      subtitle="Let's discover their interests and passions"
      stepNumber={3}
      onNext={handleNext}
      onBack={handleBack}
      nextButtonText={getNextButtonText()}
      isNextDisabled={!isStepValid()}
      showBackButton={true}
      showSkipButton={currentStep === 3}
      onSkip={currentStep === 3 ? handleNext : undefined}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        {renderCurrentQuestion()}

        {/* Progress indicator for sub-steps */}
        <View className="mt-6 flex-row justify-center">
          {[1, 2, 3].map((step) => (
            <View
              key={step}
              className={`w-2 h-2 rounded-full mx-1 ${
                step === currentStep
                  ? 'bg-red-500'
                  : step < currentStep
                    ? 'bg-green-500'
                    : isDark
                      ? 'bg-gray-600'
                      : 'bg-gray-300'
              }`}
            />
          ))}
        </View>
      </ScrollView>
    </QuestionnaireLayout>
  );
}

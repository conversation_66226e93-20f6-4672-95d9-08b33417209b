import React, { ReactNode } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { useQuestionnaire } from '../../contexts/QuestionnaireContext';
import ProgressIndicator from './ProgressIndicator';
import Button from '@/components/ui/Button';
import * as Haptics from 'expo-haptics';

interface QuestionnaireLayoutProps {
  children: ReactNode;
  title: string;
  subtitle?: string;
  stepNumber: number;
  onNext?: () => void;
  onBack?: () => void;
  nextButtonText?: string;
  isNextDisabled?: boolean;
  showBackButton?: boolean;
  showSkipButton?: boolean;
  onSkip?: () => void;
}

export default function QuestionnaireLayout({
  children,
  title,
  subtitle,
  stepNumber,
  onNext,
  onBack,
  nextButtonText = "Continue",
  isNextDisabled = false,
  showBackButton = true,
  showSkipButton = false,
  onSkip,
}: QuestionnaireLayoutProps) {
  const router = useRouter();
  const { progress, data } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const themedColors = {
    background: isDark ? '#0F0F0F' : '#FFFFFF',
    text: isDark ? '#FFFFFF' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    accent: '#E5355F',
    card: isDark ? '#1F2937' : '#F9FAFB',
    border: isDark ? '#374151' : '#E5E7EB',
  };

  const handleBack = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    if (onBack) {
      onBack();
    } else if (stepNumber > 1) {
      router.back();
    } else {
      router.push('/(app)/onboarding-questionnaire/');
    }
  };

  const handleNext = async () => {
    if (!isNextDisabled && onNext) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      onNext();
    }
  };

  const handleSkip = async () => {
    if (onSkip) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onSkip();
    }
  };

  // Get recipient name for personalization
  const recipientName = data.step1?.recipientName || 'them';

  return (
    <SafeAreaView 
      style={{ backgroundColor: themedColors.background }}
      className="flex-1"
    >
      {/* Header with Progress */}
      <Animated.View 
        entering={FadeInUp.duration(600)}
        className="px-6 pt-4 pb-2"
      >
        {/* Back Button and Step Counter */}
        <View className="flex-row items-center justify-between mb-4">
          {showBackButton ? (
            <TouchableOpacity
              onPress={handleBack}
              className="p-2 rounded-full"
              style={{ backgroundColor: themedColors.card }}
            >
              <Feather name="arrow-left" size={20} color={themedColors.text} />
            </TouchableOpacity>
          ) : (
            <View className="w-10" />
          )}
          
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-sm font-medium"
          >
            {stepNumber} of {progress.totalSteps}
          </Text>
          
          {showSkipButton ? (
            <TouchableOpacity onPress={handleSkip}>
              <Text 
                style={{ color: themedColors.accent }}
                className="text-sm font-medium"
              >
                Skip
              </Text>
            </TouchableOpacity>
          ) : (
            <View className="w-10" />
          )}
        </View>

        {/* Progress Bar */}
        <ProgressIndicator />
      </Animated.View>

      {/* Content Area */}
      <View className="flex-1 px-6">
        {/* Title Section */}
        <Animated.View 
          entering={FadeInDown.duration(600).delay(200)}
          className="mb-8"
        >
          <Text 
            style={{ color: themedColors.text }}
            className="mb-2 text-2xl font-bold"
          >
            {title.replace('[Name]', recipientName)}
          </Text>
          {subtitle && (
            <Text 
              style={{ color: themedColors.textSecondary }}
              className="text-base leading-relaxed"
            >
              {subtitle.replace('[Name]', recipientName)}
            </Text>
          )}
        </Animated.View>

        {/* Question Content */}
        <Animated.View 
          entering={FadeInDown.duration(600).delay(400)}
          className="flex-1"
        >
          {children}
        </Animated.View>
      </View>

      {/* Bottom Navigation */}
      <Animated.View 
        entering={FadeInDown.duration(600).delay(600)}
        className="px-6 pb-6 pt-4"
        style={{ borderTopColor: themedColors.border, borderTopWidth: 1 }}
      >
        <View className="flex-row items-center justify-between">
          {/* Time Estimate */}
          <View className="flex-1">
            <Text 
              style={{ color: themedColors.textSecondary }}
              className="text-xs"
            >
              About {Math.ceil(progress.estimatedTimeRemaining / 60)} min remaining
            </Text>
          </View>

          {/* Next Button */}
          <View className="flex-1">
            <Button
              title={nextButtonText}
              onPress={handleNext}
              variant="primary"
              disabled={isNextDisabled}
              className="rounded-xl"
              style={{ 
                backgroundColor: isNextDisabled ? themedColors.textSecondary : themedColors.accent,
                opacity: isNextDisabled ? 0.5 : 1,
              }}
            />
          </View>
        </View>
      </Animated.View>
    </SafeAreaView>
  );
}

import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import QuestionnaireLayout from '../../../components/questionnaire/QuestionnaireLayout';
import VisualGrid from '../../../components/questionnaire/QuestionTypes/VisualGrid';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { QuestionnaireStep7Data } from '../../../types/questionnaire';
import { useColorScheme } from 'nativewind';

const specialConsiderations = [
  { id: 'allergies', label: 'Has Allergies', icon: 'alert-triangle', image: require('@/assets/images/onboarding1.png') },
  { id: 'dietary', label: 'Dietary Restrictions', icon: 'coffee', image: require('@/assets/images/onboarding2.png') },
  { id: 'space', label: 'Limited Space', icon: 'home', image: require('@/assets/images/onboarding3.png') },
  { id: 'eco-conscious', label: 'Eco-Conscious', icon: 'leaf', image: require('@/assets/images/onboarding1.png') },
  { id: 'tech-averse', label: 'Not Tech-Savvy', icon: 'smartphone', image: require('@/assets/images/onboarding2.png') },
  { id: 'minimalist', label: 'Prefers Minimal Items', icon: 'minus-circle', image: require('@/assets/images/onboarding3.png') },
];

const surprisePreferences = [
  { id: 'loves-surprises', label: 'Loves Surprises', icon: 'gift', image: require('@/assets/images/onboarding1.png') },
  { id: 'prefers-hints', label: 'Prefers Hints', icon: 'eye', image: require('@/assets/images/onboarding2.png') },
  { id: 'wants-to-know', label: 'Wants to Know', icon: 'list', image: require('@/assets/images/onboarding3.png') },
];

const communicationStyles = [
  { id: 'direct', label: 'Direct & Clear', icon: 'message-circle', image: require('@/assets/images/onboarding1.png') },
  { id: 'subtle', label: 'Subtle Hints', icon: 'eye', image: require('@/assets/images/onboarding2.png') },
  { id: 'wishlist', label: 'Shares Wishlists', icon: 'list', image: require('@/assets/images/onboarding3.png') },
  { id: 'never', label: 'Never Mentions Wants', icon: 'help-circle', image: require('@/assets/images/onboarding1.png') },
];

export default function Step7() {
  const router = useRouter();
  const { data, updateStepData, completeStep, addBadge } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [currentStep, setCurrentStep] = useState(1); // 1: special considerations, 2: surprise preference, 3: communication
  const [stepData, setStepData] = useState<QuestionnaireStep7Data>({
    specialConsiderations: data.step7?.specialConsiderations || [],
    communicationStyle: data.step7?.communicationStyle || '',
    surprisePreference: data.step7?.surprisePreference || 'loves-surprises',
  });

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return true; // Optional
      case 2:
        return stepData.surprisePreference.length > 0;
      case 3:
        return stepData.communicationStyle.length > 0;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete step and move to completion
      updateStepData(7, stepData);
      completeStep(7);

      // Award profile master badge
      addBadge({
        id: 'profile-master',
        name: 'Profile Master',
        description: 'Completed the full questionnaire',
        icon: '🏆',
        category: 'completion',
        unlockedAt: new Date(),
      });

      router.push('/(app)/onboarding-questionnaire/complete');
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const updateAnswer = (field: keyof QuestionnaireStep7Data, value: any) => {
    setStepData(prev => ({ ...prev, [field]: value }));
  };

  const recipientName = data.step1?.recipientName || 'them';

  const renderCurrentQuestion = () => {
    switch (currentStep) {
      case 1:
        return (
          <VisualGrid
            title={`Any special considerations for ${recipientName}?`}
            subtitle="Select anything we should keep in mind (optional)"
            items={specialConsiderations}
            columns={2}
            allowMultiple={true}
            maxSelections={4}
            onAnswer={(value) => updateAnswer('specialConsiderations', value)}
            currentAnswer={stepData.specialConsiderations}
            isRequired={false}
          />
        );

      case 2:
        return (
          <VisualGrid
            title={`How does ${recipientName} feel about surprises?`}
            subtitle="This helps us suggest the right approach to gift-giving"
            items={surprisePreferences}
            columns={1}
            allowMultiple={false}
            onAnswer={(value) => updateAnswer('surprisePreference', value)}
            currentAnswer={stepData.surprisePreference}
            isRequired={true}
          />
        );

      case 3:
        return (
          <VisualGrid
            title={`How does ${recipientName} usually express what they want?`}
            subtitle="Understanding their communication style helps us give better suggestions"
            items={communicationStyles}
            columns={2}
            allowMultiple={false}
            onAnswer={(value) => updateAnswer('communicationStyle', value)}
            currentAnswer={stepData.communicationStyle}
            isRequired={true}
          />
        );

      default:
        return null;
    }
  };

  const getNextButtonText = () => {
    if (currentStep < 3) return "Continue";
    return "Complete Profile!";
  };

  return (
    <QuestionnaireLayout
      title="Final Touches"
      subtitle="Just a few more details to perfect their profile"
      stepNumber={7}
      onNext={handleNext}
      onBack={handleBack}
      nextButtonText={getNextButtonText()}
      isNextDisabled={!isStepValid()}
      showBackButton={true}
      showSkipButton={currentStep === 1}
      onSkip={currentStep === 1 ? () => setCurrentStep(2) : undefined}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        {renderCurrentQuestion()}

        {/* Progress indicator for sub-steps */}
        <View className="mt-6 flex-row justify-center">
          {[1, 2, 3].map((step) => (
            <View
              key={step}
              className={`w-2 h-2 rounded-full mx-1 ${
                step === currentStep
                  ? 'bg-red-500'
                  : step < currentStep
                    ? 'bg-green-500'
                    : isDark
                      ? 'bg-gray-600'
                      : 'bg-gray-300'
              }`}
            />
          ))}
        </View>
      </ScrollView>
    </QuestionnaireLayout>
  );
}

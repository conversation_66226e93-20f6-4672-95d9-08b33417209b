import { Stack } from "expo-router";
import { QuestionnaireProvider } from "../../../contexts/QuestionnaireContext";

export default function QuestionnaireLayout() {
  return (
    <QuestionnaireProvider>
      <Stack 
        screenOptions={{ 
          headerShown: false,
          gestureEnabled: false, // Prevent swipe back to maintain flow
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen name="index" />
        <Stack.Screen name="step-1" />
        <Stack.Screen name="step-2" />
        <Stack.Screen name="step-3" />
        <Stack.Screen name="step-4" />
        <Stack.Screen name="step-5" />
        <Stack.Screen name="step-6" />
        <Stack.Screen name="step-7" />
        <Stack.Screen name="complete" />
      </Stack>
    </QuestionnaireProvider>
  );
}

import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import * as Haptics from 'expo-haptics';

interface ColorOption {
  id: string;
  name: string;
  colors: string[];
  description?: string;
}

interface ColorPaletteProps {
  title: string;
  subtitle?: string;
  colors: ColorOption[];
  allowMultiple?: boolean;
  maxSelections?: number;
  onAnswer: (selectedColors: string[]) => void;
  currentAnswer?: string[];
  isRequired?: boolean;
}

export default function ColorPalette({
  title,
  subtitle,
  colors,
  allowMultiple = true,
  maxSelections = 3,
  onAnswer,
  currentAnswer = [],
  isRequired = false,
}: ColorPaletteProps) {
  const [selectedColors, setSelectedColors] = useState<string[]>(currentAnswer);
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const celebrationScale = useSharedValue(1);

  const themedColors = {
    background: isDark ? '#1F2937' : '#F9FAFB',
    text: isDark ? '#FFFFFF' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    border: isDark ? '#374151' : '#E5E7EB',
    accent: '#E5355F',
  };

  useEffect(() => {
    onAnswer(selectedColors);
  }, [selectedColors]);

  const handleColorPress = (colorId: string) => {
    if (allowMultiple) {
      setSelectedColors(prev => {
        const isSelected = prev.includes(colorId);
        let newSelection;
        
        if (isSelected) {
          newSelection = prev.filter(id => id !== colorId);
        } else {
          if (prev.length < maxSelections) {
            newSelection = [...prev, colorId];
          } else {
            newSelection = [...prev.slice(1), colorId];
          }
        }
        
        return newSelection;
      });
    } else {
      setSelectedColors([colorId]);
    }

    celebrationScale.value = withSpring(1.1, {}, () => {
      celebrationScale.value = withSpring(1);
    });

    runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Medium);
  };

  const renderColorOption = (colorOption: ColorOption) => {
    const isSelected = selectedColors.includes(colorOption.id);
    const animatedScale = useSharedValue(1);

    const itemStyle = useAnimatedStyle(() => {
      return {
        transform: [{ scale: animatedScale.value }],
      };
    });

    const handlePress = () => {
      animatedScale.value = withSpring(0.95, {}, () => {
        animatedScale.value = withSpring(1);
      });
      handleColorPress(colorOption.id);
    };

    return (
      <Animated.View
        key={colorOption.id}
        style={itemStyle}
        className="mb-4"
      >
        <TouchableOpacity
          onPress={handlePress}
          className="p-4 rounded-2xl border-2"
          style={{
            backgroundColor: themedColors.background,
            borderColor: isSelected ? themedColors.accent : themedColors.border,
          }}
        >
          {/* Color Swatches */}
          <View className="flex-row justify-center mb-3">
            {colorOption.colors.map((color, index) => (
              <View
                key={index}
                className="w-8 h-8 rounded-full mx-1 border border-gray-200"
                style={{ backgroundColor: color }}
              />
            ))}
          </View>

          {/* Color Name */}
          <Text 
            style={{ color: themedColors.text }}
            className="text-center font-semibold mb-1"
          >
            {colorOption.name}
          </Text>

          {/* Description */}
          {colorOption.description && (
            <Text 
              style={{ color: themedColors.textSecondary }}
              className="text-xs text-center"
            >
              {colorOption.description}
            </Text>
          )}

          {/* Selection Indicator */}
          {isSelected && (
            <View className="absolute top-2 right-2">
              <Feather 
                name="check-circle"
                size={16}
                color={themedColors.accent}
              />
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const celebrationStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: celebrationScale.value }],
    };
  });

  return (
    <View className="w-full">
      {/* Title */}
      <Animated.View style={celebrationStyle} className="mb-6">
        <Text 
          style={{ color: themedColors.text }}
          className="text-xl font-bold mb-2"
        >
          {title}
          {isRequired && <Text style={{ color: themedColors.accent }}> *</Text>}
        </Text>
        {subtitle && (
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-base leading-relaxed"
          >
            {subtitle}
          </Text>
        )}
      </Animated.View>

      {/* Color Options */}
      <View className="mb-4">
        {colors.map(renderColorOption)}
      </View>

      {/* Selection Info */}
      {allowMultiple && (
        <View className="mb-4">
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-sm text-center"
          >
            {selectedColors.length === 0 
              ? `Select up to ${maxSelections} color palettes`
              : `${selectedColors.length}/${maxSelections} selected`
            }
          </Text>
        </View>
      )}

      {/* Validation Message */}
      {isRequired && selectedColors.length === 0 && (
        <Text 
          style={{ color: themedColors.accent }}
          className="text-sm mt-1"
        >
          Please select at least one color palette
        </Text>
      )}

      {/* Encouragement Message */}
      {selectedColors.length > 0 && (
        <Animated.View 
          entering={withSpring(1)}
          className="mt-2"
        >
          <Text 
            style={{ color: themedColors.accent }}
            className="text-sm font-medium text-center"
          >
            {selectedColors.length === 1 ? "Beautiful choice! 🎨" : 
             allowMultiple && selectedColors.length < maxSelections ? "Great taste! Feel free to select more ✨" : 
             "Perfect color sense! 🌈"}
          </Text>
        </Animated.View>
      )}
    </View>
  );
}

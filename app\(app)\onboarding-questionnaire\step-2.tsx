import React, { useState } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import QuestionnaireLayout from '../../../components/questionnaire/QuestionnaireLayout';
import VisualGrid from '../../../components/questionnaire/QuestionTypes/VisualGrid';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { QuestionnaireStep2Data } from '../../../types/questionnaire';
import { useColorScheme } from 'nativewind';

const lifestyleOptions = [
  { id: 'active', label: 'Active & Sporty', icon: 'activity' },
  { id: 'homebody', label: 'Homebody', icon: 'home' },
  { id: 'foodie', label: 'Foodie', icon: 'coffee' },
  { id: 'tech-savvy', label: 'Tech Savvy', icon: 'smartphone' },
  { id: 'artistic', label: 'Artistic', icon: 'palette' },
  { id: 'outdoorsy', label: 'Outdoorsy', icon: 'sun' },
  { id: 'bookworm', label: 'Bookworm', icon: 'book' },
  { id: 'social', label: 'Social Butterfly', icon: 'users' },
  { id: 'minimalist', label: 'Minimalist', icon: 'minus-circle' },
  { id: 'collector', label: 'Collector', icon: 'archive' },
];

const ageRangeOptions = [
  { id: 'teen', label: 'Teen (13-19)', icon: 'zap' },
  { id: 'young-adult', label: 'Young Adult (20-29)', icon: 'trending-up' },
  { id: 'adult', label: 'Adult (30-39)', icon: 'briefcase' },
  { id: 'middle-aged', label: 'Middle-aged (40-59)', icon: 'award' },
  { id: 'senior', label: 'Senior (60+)', icon: 'heart' },
];

export default function Step2() {
  const router = useRouter();
  const { data, updateStepData, completeStep, addBadge } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [currentStep, setCurrentStep] = useState(1); // 1: lifestyle, 2: age range
  const [stepData, setStepData] = useState<QuestionnaireStep2Data>({
    personalityType: data.step2?.personalityType || {
      introvertExtrovert: 'ambivert',
      practicalCreative: 'balanced',
      traditionalTrendy: 'eclectic',
    },
    lifestyleTags: data.step2?.lifestyleTags || [],
    ageRange: data.step2?.ageRange || '',
  });

  const themedColors = {
    text: isDark ? '#FFFFFF' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    accent: '#E5355F',
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return stepData.lifestyleTags.length > 0;
      case 2:
        return stepData.ageRange.length > 0;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete step and move to next questionnaire step
      updateStepData(2, stepData);
      completeStep(2);

      // Award personality detective badge
      addBadge({
        id: 'personality-detective',
        name: 'Personality Detective',
        description: 'Discovered their personality traits',
        icon: '🕵️',
        category: 'progress',
        unlockedAt: new Date(),
      });

      router.push('/(app)/onboarding-questionnaire/step-3');
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const updateAnswer = (field: keyof QuestionnaireStep2Data, value: any) => {
    setStepData(prev => ({ ...prev, [field]: value }));
  };

  const recipientName = data.step1?.recipientName || 'them';

  const renderCurrentQuestion = () => {
    switch (currentStep) {
      case 1:
        return (
          <VisualGrid
            title={`What describes ${recipientName}?`}
            subtitle="Select all lifestyle tags that fit their personality"
            items={lifestyleOptions}
            columns={2}
            allowMultiple={true}
            maxSelections={5}
            onAnswer={(value) => updateAnswer('lifestyleTags', value)}
            currentAnswer={stepData.lifestyleTags}
            isRequired={true}
          />
        );

      case 2:
        return (
          <VisualGrid
            title={`What's ${recipientName}'s age range?`}
            subtitle="This helps us suggest age-appropriate gifts"
            items={ageRangeOptions}
            columns={1}
            allowMultiple={false}
            onAnswer={(value) => updateAnswer('ageRange', value)}
            currentAnswer={stepData.ageRange}
            isRequired={true}
          />
        );

      default:
        return null;
    }
  };

  const getNextButtonText = () => {
    if (currentStep < 2) return "Continue";
    return "Next Step";
  };

  return (
    <QuestionnaireLayout
      title={`What's ${recipientName} like?`}
      subtitle="Help us understand their personality"
      stepNumber={2}
      onNext={handleNext}
      onBack={handleBack}
      nextButtonText={getNextButtonText()}
      isNextDisabled={!isStepValid()}
      showBackButton={true}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        {renderCurrentQuestion()}

        {/* Progress indicator for sub-steps */}
        <View className="mt-6 flex-row justify-center">
          {[1, 2].map((step) => (
            <View
              key={step}
              className={`w-2 h-2 rounded-full mx-1 ${
                step === currentStep
                  ? 'bg-red-500'
                  : step < currentStep
                    ? 'bg-green-500'
                    : isDark
                      ? 'bg-gray-600'
                      : 'bg-gray-300'
              }`}
            />
          ))}
        </View>
      </ScrollView>
    </QuestionnaireLayout>
  );
}

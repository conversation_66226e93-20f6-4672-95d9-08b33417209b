import React, { useEffect } from 'react';
import { View, Text, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import Button from '@/components/ui/Button';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { useColorScheme } from 'nativewind';
import * as Haptics from 'expo-haptics';

export default function QuestionnaireIntro() {
  const router = useRouter();
  const { resetQuestionnaire, loadProgress } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  useEffect(() => {
    // Load any existing progress when component mounts
    loadProgress();
  }, []);

  const handleStart = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    // Reset questionnaire to ensure clean start
    resetQuestionnaire();
    router.push('/(app)/onboarding-questionnaire/step-1');
  };

  const themedColors = {
    background: isDark ? '#0F0F0F' : '#FFFFFF',
    text: isDark ? '#FFFFFF' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    accent: '#E5355F',
    card: isDark ? '#1F2937' : '#F9FAFB',
  };

  return (
    <SafeAreaView 
      style={{ backgroundColor: themedColors.background }}
      className="flex-1"
    >
      <View className="flex-1 px-6 py-8">
        {/* Header Image */}
        <Animated.View 
          entering={FadeInUp.duration(800).delay(200)}
          className="items-center mb-8"
        >
          <Image 
            source={require('@/assets/images/onboarding1.png')}
            className="w-64 h-64"
            resizeMode="contain"
          />
        </Animated.View>

        {/* Content */}
        <Animated.View 
          entering={FadeInDown.duration(800).delay(400)}
          className="flex-1 justify-center"
        >
          <Text 
            style={{ color: themedColors.text }}
            className="mb-4 text-3xl font-bold text-center"
          >
            Let's create the perfect gift profile! 🎁
          </Text>
          
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="mb-8 text-lg text-center leading-relaxed"
          >
            We'll ask you a few fun questions to understand who you're shopping for. 
            This takes about 3 minutes and makes finding amazing gifts so much easier!
          </Text>

          {/* Features List */}
          <View className="mb-8 space-y-4">
            <FeatureItem 
              icon="🎯" 
              text="Personalized gift recommendations"
              textColor={themedColors.text}
            />
            <FeatureItem 
              icon="⚡" 
              text="Quick 7-step process"
              textColor={themedColors.text}
            />
            <FeatureItem 
              icon="🏆" 
              text="Unlock insights about their personality"
              textColor={themedColors.text}
            />
          </View>
        </Animated.View>

        {/* Start Button */}
        <Animated.View 
          entering={FadeInDown.duration(800).delay(600)}
          className="pb-4"
        >
          <Button
            title="Let's Get Started!"
            onPress={handleStart}
            variant="primary"
            className="w-full py-4 rounded-xl shadow-lg"
            style={{ backgroundColor: themedColors.accent }}
          />
          
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="mt-4 text-sm text-center"
          >
            Takes about 3 minutes • Your data stays private
          </Text>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}

interface FeatureItemProps {
  icon: string;
  text: string;
  textColor: string;
}

function FeatureItem({ icon, text, textColor }: FeatureItemProps) {
  return (
    <View className="flex-row items-center">
      <Text className="mr-3 text-2xl">{icon}</Text>
      <Text style={{ color: textColor }} className="text-base font-medium">
        {text}
      </Text>
    </View>
  );
}

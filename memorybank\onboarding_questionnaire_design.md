# Onboarding Questionnaire Design

## 1. Introduction

This document outlines the design for a new onboarding questionnaire for the Giftmi app. The goal is to create a fun, engaging, and "addicting" experience that captures key user information to pre-populate their profile, making the profile creation process smoother and more enjoyable.

## 2. User Flow

The questionnaire is designed as a multi-step process, with a clear progression from one question to the next. A progress bar will be visible at all times to keep the user informed of their progress.

```mermaid
graph TD
    A[Start] --> B{Welcome Screen};
    B --> C{Question 1: Interests};
    C --> D{Question 2: Dislikes};
    D --> E{Question 3: Favorite Color};
    E --> F{Question 4: Preferred Style};
    F --> G{Question 5: Clothing Size};
    G --> H{Question 6: Shoe Size};
    H --> I{Completion Screen};
    I --> J[End];
```

### Flow Description:

1.  **Welcome Screen:** A friendly welcome message that explains the purpose of the questionnaire and how it will help personalize their experience.
2.  **Core Questions (Interests & Dislikes):** These questions are prioritized to gather the most critical information for gift recommendations.
3.  **Style & Preference Questions:** These questions delve into the user's aesthetic preferences, using visual aids to make the experience more interactive.
4.  **Sizing Information:** Practical questions to ensure gift suggestions are the right fit.
5.  **Completion Screen:** A confirmation screen that congratulates the user on completing the questionnaire and informs them that their profile has been pre-populated with their answers.


## 3. Question Design

The following questions are designed to be fun, easy to answer, and directly map to the fields in the existing profile form.

### Question 1: Interests
*   **Question Text:** "What are some of their favorite things?"
*   **Question Type:** Tag Input (with suggestions)
*   **Maps to:** `interests`
*   **Notes:** This will be a free-form input field where the user can add multiple interests as tags. We will provide a list of suggested interests to guide them.

### Question 2: Dislikes
*   **Question Text:** "What are some things they're not a fan of?"
*   **Question Type:** Tag Input (with suggestions)
*   **Maps to:** `dislikes`
*   **Notes:** Similar to the interests question, this will be a free-form tag input.

### Question 3: Favorite Color
*   **Question Text:** "If they had to pick a favorite color, what would it be?"
*   **Question Type:** Color Grid
*   **Maps to:** `preferences.favoriteColor`
*   **Notes:** A visually appealing grid of color swatches will be displayed. The user can select one color. The options will be drawn from [`constants/profileOptions.ts`](constants/profileOptions.ts:1).

### Question 4: Preferred Style
*   **Question Text:** "How would you describe their personal style?"
*   **Question Type:** Image Selection
*   **Maps to:** `preferences.preferredStyle`
*   **Notes:** A series of images representing different styles (e.g., Casual, Sporty, Bohemian) will be shown. The user can select the one that best represents the person's style. The options will be based on [`constants/profileOptions.ts`](constants/profileOptions.ts:64).

### Question 5: Clothing Size
*   **Question Text:** "What's their go-to clothing size?"
*   **Question Type:** Grid Selection
*   **Maps to:** `sizes.clothing`
*   **Notes:** A grid of common clothing sizes will be displayed for easy selection. The options will be from [`constants/profileOptions.ts`](constants/profileOptions.ts:144).

### Question 6: Shoe Size
*   **Question Text:** "And their shoe size?"
*   **Question Type:** Grid Selection
*   **Maps to:** `sizes.shoe`
*   **Notes:** A grid of common shoe sizes will be displayed. The options will be from [`constants/profileOptions.ts`](constants/profileOptions.ts:314).


## 4. UI/UX Design

The questionnaire's UI/UX will be clean, modern, and playful to create an enjoyable experience.

*   **Visual Style:**
    *   **Color Palette:** A vibrant and inviting color palette will be used, consistent with the Giftmi brand.
    *   **Typography:** Clean and legible fonts will be used for all text.
    *   **Illustrations:** Custom illustrations and icons will be used to add a touch of personality.

*   **Interactivity and Engagement:**
    *   **Animations:** Subtle animations will be used for screen transitions and to provide feedback on user interactions.
    *   **Progress Indicator:** A visually engaging progress bar will be displayed at the top of the screen to show the user how far they've progressed.
    *   **Micro-interactions:** Small, delightful animations will be used for actions like button clicks and selections to make the interface feel more responsive and alive.

*   **Layout:**
    *   **One Question Per Screen:** To avoid overwhelming the user, only one question will be displayed at a time.
    *   **Clear Call-to-Actions:** Buttons for navigation (e.g., "Next," "Skip") will be prominently displayed.

import { QuestionnaireData } from '../types/questionnaire';

// Map questionnaire data to profile form data
export const mapQuestionnaireToProfile = (questionnaireData: QuestionnaireData) => {
  const profileData: any = {};

  // Step 1: Basic Information
  if (questionnaireData.step1) {
    profileData.name = questionnaireData.step1.recipientName;
    profileData.relationship = questionnaireData.step1.relationship;
    
    // Map gift occasions to special dates
    profileData.specialDates = questionnaireData.step1.giftOccasions.map(occasion => ({
      name: occasion.charAt(0).toUpperCase() + occasion.slice(1),
      date: new Date(), // Default to today, user can edit later
      type: occasion,
    }));
  }

  // Step 2: Personality and Demographics
  if (questionnaireData.step2) {
    profileData.interests = questionnaireData.step2.lifestyleTags;
    profileData.ageRange = questionnaireData.step2.ageRange;
    profileData.personalityTraits = [
      questionnaireData.step2.personalityType.introvertExtrovert,
      questionnaireData.step2.personalityType.practicalCreative,
      questionnaireData.step2.personalityType.traditionalTrendy,
    ].filter(Boolean);
  }

  // Step 3: Interests and Hobbies
  if (questionnaireData.step3) {
    profileData.interests = [
      ...(profileData.interests || []),
      ...questionnaireData.step3.interestCategories,
    ];
    profileData.hobbyLevel = questionnaireData.step3.hobbyLevel;
    
    if (questionnaireData.step3.uniquePassion) {
      profileData.notes = (profileData.notes || '') + 
        `Special Interest: ${questionnaireData.step3.uniquePassion}\n`;
    }
  }

  // Step 4: Style Preferences
  if (questionnaireData.step4) {
    profileData.favoriteColors = questionnaireData.step4.stylePreferences.colorPalette;
    profileData.fashionStyle = questionnaireData.step4.stylePreferences.fashionStyle;
    profileData.homeDecorStyle = questionnaireData.step4.stylePreferences.homeDecorStyle;
  }

  // Step 5: Budget and Value
  if (questionnaireData.step5) {
    profileData.budgetMin = questionnaireData.step5.budgetRange.min;
    profileData.budgetMax = questionnaireData.step5.budgetRange.max;
    profileData.spendingComfort = questionnaireData.step5.spendingComfort;
    profileData.valuePhilosophy = questionnaireData.step5.valuePhilosophy;
  }

  // Step 6: Gift History
  if (questionnaireData.step6) {
    const giftHistory = questionnaireData.step6.giftHistory;
    
    if (giftHistory.bestGiftGiven) {
      profileData.pastGifts = [{
        gift: giftHistory.bestGiftGiven,
        reaction: 'positive',
        notes: 'Best gift given according to questionnaire',
      }];
    }
    
    profileData.reactionStyle = giftHistory.theirReactionStyle;
    
    if (giftHistory.worstGiftGiven) {
      profileData.dislikes = [giftHistory.worstGiftGiven];
    }
  }

  // Step 7: Final Considerations
  if (questionnaireData.step7) {
    profileData.specialConsiderations = questionnaireData.step7.specialConsiderations;
    profileData.surprisePreference = questionnaireData.step7.surprisePreference;
    profileData.communicationStyle = questionnaireData.step7.communicationStyle;
    
    // Add special considerations to notes
    if (questionnaireData.step7.specialConsiderations.length > 0) {
      profileData.notes = (profileData.notes || '') + 
        `Special Considerations: ${questionnaireData.step7.specialConsiderations.join(', ')}\n`;
    }
  }

  // Generate personality insights
  profileData.personalityInsights = generatePersonalityInsights(questionnaireData);

  return profileData;
};

// Generate personality insights based on questionnaire responses
export const generatePersonalityInsights = (data: QuestionnaireData): string[] => {
  const insights: string[] = [];

  // Relationship-based insights
  if (data.step1?.relationship) {
    const relationshipInsights = {
      'partner': 'Intimate relationship - focus on romantic and personal gifts',
      'spouse': 'Long-term partnership - consider shared experiences and meaningful items',
      'family': 'Family bond - traditional and sentimental gifts work well',
      'friend': 'Friendship - fun, social, and interest-based gifts are great',
      'colleague': 'Professional relationship - keep gifts appropriate and thoughtful',
    };
    
    const insight = relationshipInsights[data.step1.relationship as keyof typeof relationshipInsights];
    if (insight) insights.push(insight);
  }

  // Lifestyle insights
  if (data.step2?.lifestyleTags) {
    if (data.step2.lifestyleTags.includes('active')) {
      insights.push('Active lifestyle - consider fitness, outdoor, and sports-related gifts');
    }
    if (data.step2.lifestyleTags.includes('homebody')) {
      insights.push('Enjoys home comfort - cozy, indoor, and home improvement gifts are ideal');
    }
    if (data.step2.lifestyleTags.includes('foodie')) {
      insights.push('Food enthusiast - culinary experiences, cooking tools, and gourmet items');
    }
    if (data.step2.lifestyleTags.includes('tech-savvy')) {
      insights.push('Technology lover - gadgets, apps, and tech accessories are appreciated');
    }
  }

  // Interest-based insights
  if (data.step3?.interestCategories) {
    const interests = data.step3.interestCategories;
    if (interests.length >= 3) {
      insights.push('Diverse interests - consider gifts that combine multiple passions');
    }
    
    if (interests.includes('art') && interests.includes('crafts')) {
      insights.push('Creative soul - art supplies, DIY kits, and handmade items resonate');
    }
    
    if (interests.includes('music') || interests.includes('reading')) {
      insights.push('Appreciates culture - books, music, and cultural experiences are valued');
    }
  }

  // Style insights
  if (data.step4?.stylePreferences) {
    const style = data.step4.stylePreferences;
    if (style.fashionStyle === 'minimalist' || style.homeDecorStyle === 'minimalist') {
      insights.push('Minimalist aesthetic - prefers quality over quantity, clean designs');
    }
    if (style.colorPalette?.includes('bold')) {
      insights.push('Bold personality - not afraid of vibrant colors and statement pieces');
    }
  }

  // Budget insights
  if (data.step5?.spendingComfort) {
    const budgetInsights = {
      'conservative': 'Appreciates thoughtful, budget-conscious gifts',
      'moderate': 'Values balance between quality and price',
      'generous': 'Open to higher-end, luxury gift options',
    };
    
    const insight = budgetInsights[data.step5.spendingComfort as keyof typeof budgetInsights];
    if (insight) insights.push(insight);
  }

  // Communication insights
  if (data.step7?.surprisePreference) {
    const surpriseInsights = {
      'loves-surprises': 'Enjoys the element of surprise - unexpected gifts create joy',
      'prefers-hints': 'Likes some guidance - subtle hints about preferences help',
      'wants-to-know': 'Prefers transparency - discussing gift ideas beforehand works best',
    };
    
    const insight = surpriseInsights[data.step7.surprisePreference as keyof typeof surpriseInsights];
    if (insight) insights.push(insight);
  }

  return insights;
};

// Generate gift recommendations based on questionnaire data
export const generateGiftRecommendations = (data: QuestionnaireData): string[] => {
  const recommendations: string[] = [];

  // Interest-based recommendations
  if (data.step3?.interestCategories) {
    const interestMap = {
      'sports': ['Fitness tracker', 'Sports equipment', 'Athletic wear', 'Gym membership'],
      'music': ['Headphones', 'Vinyl records', 'Concert tickets', 'Music lessons'],
      'art': ['Art supplies', 'Museum membership', 'Art prints', 'Creative workshop'],
      'technology': ['Latest gadgets', 'Tech accessories', 'Smart home devices', 'Software subscriptions'],
      'cooking': ['Kitchen gadgets', 'Cookbook', 'Cooking class', 'Gourmet ingredients'],
      'travel': ['Travel accessories', 'Experience vouchers', 'Travel guides', 'Luggage'],
      'reading': ['Books', 'E-reader', 'Bookstore gift card', 'Reading accessories'],
      'gaming': ['Video games', 'Gaming accessories', 'Gaming chair', 'Gaming subscription'],
    };

    data.step3.interestCategories.forEach(interest => {
      const recs = interestMap[interest as keyof typeof interestMap];
      if (recs) recommendations.push(...recs);
    });
  }

  // Budget-based filtering
  if (data.step5?.budgetRange) {
    const { min, max } = data.step5.budgetRange;
    
    if (max < 50) {
      recommendations.push('Small accessories', 'Books', 'Candles', 'Snacks');
    } else if (max < 100) {
      recommendations.push('Nice accessories', 'Quality items', 'Experience vouchers');
    } else if (max < 200) {
      recommendations.push('Premium items', 'Electronics', 'Experiences');
    } else {
      recommendations.push('Luxury items', 'High-end electronics', 'Premium experiences');
    }
  }

  // Remove duplicates and return
  return [...new Set(recommendations)];
};

// Calculate compatibility score for gift suggestions
export const calculateGiftCompatibility = (
  giftCategories: string[],
  questionnaireData: QuestionnaireData
): number => {
  let score = 0;
  let maxScore = 0;

  // Interest matching
  if (questionnaireData.step3?.interestCategories) {
    maxScore += 40;
    const matchingInterests = giftCategories.filter(category =>
      questionnaireData.step3!.interestCategories.includes(category)
    );
    score += (matchingInterests.length / giftCategories.length) * 40;
  }

  // Style matching
  if (questionnaireData.step4?.stylePreferences) {
    maxScore += 30;
    // Add style matching logic here
    score += 15; // Default partial score
  }

  // Budget compatibility
  if (questionnaireData.step5?.budgetRange) {
    maxScore += 30;
    // Add budget matching logic here
    score += 20; // Default partial score
  }

  return maxScore > 0 ? (score / maxScore) * 100 : 0;
};

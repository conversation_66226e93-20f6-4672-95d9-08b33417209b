import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import { VisualGridProps } from '../../../types/questionnaire';
import * as Haptics from 'expo-haptics';

export default function VisualGrid({
  title,
  subtitle,
  items,
  columns = 2,
  allowMultiple = false,
  maxSelections = 3,
  onAnswer,
  currentAnswer = allowMultiple ? [] : null,
  isRequired = false,
}: VisualGridProps) {
  const [selectedItems, setSelectedItems] = useState<string[]>(
    allowMultiple 
      ? (Array.isArray(currentAnswer) ? currentAnswer : [])
      : (currentAnswer ? [currentAnswer] : [])
  );
  
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const celebrationScale = useSharedValue(1);

  const themedColors = {
    background: isDark ? '#1F2937' : '#F9FAFB',
    backgroundSelected: '#E5355F',
    border: isDark ? '#374151' : '#E5E7EB',
    borderSelected: '#E5355F',
    text: isDark ? '#FFFFFF' : '#1F2937',
    textSelected: '#FFFFFF',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    icon: isDark ? '#9CA3AF' : '#6B7280',
    iconSelected: '#FFFFFF',
  };

  useEffect(() => {
    if (allowMultiple) {
      onAnswer(selectedItems);
    } else {
      onAnswer(selectedItems[0] || null);
    }
  }, [selectedItems, allowMultiple, onAnswer]);

  const handleItemPress = (itemId: string) => {
    if (allowMultiple) {
      setSelectedItems(prev => {
        const isSelected = prev.includes(itemId);
        let newSelection;
        
        if (isSelected) {
          // Remove item
          newSelection = prev.filter(id => id !== itemId);
        } else {
          // Add item (respect max selections)
          if (prev.length < maxSelections) {
            newSelection = [...prev, itemId];
          } else {
            // Replace oldest selection
            newSelection = [...prev.slice(1), itemId];
          }
        }
        
        return newSelection;
      });
    } else {
      setSelectedItems([itemId]);
    }

    // Celebration animation
    celebrationScale.value = withSpring(1.1, {}, () => {
      celebrationScale.value = withSpring(1);
    });

    runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Medium);
  };

  const renderItem = (item: any, index: number) => {
    const isSelected = selectedItems.includes(item.id);
    const animatedScale = useSharedValue(1);

    const itemStyle = useAnimatedStyle(() => {
      return {
        transform: [{ scale: animatedScale.value }],
      };
    });

    const handlePress = () => {
      animatedScale.value = withSpring(0.95, {}, () => {
        animatedScale.value = withSpring(1);
      });
      handleItemPress(item.id);
    };

    return (
      <Animated.View
        key={item.id}
        style={itemStyle}
        className="flex-1 mx-1 mb-3"
      >
        <TouchableOpacity
          onPress={handlePress}
          className="p-4 rounded-2xl border-2 min-h-[100px] justify-center items-center"
          style={{
            backgroundColor: isSelected ? themedColors.backgroundSelected : themedColors.background,
            borderColor: isSelected ? themedColors.borderSelected : themedColors.border,
          }}
        >
          {/* Icon or Image */}
          {item.image ? (
            <Image
              source={typeof item.image === 'string' ? { uri: item.image } : item.image}
              className="w-12 h-12 mb-2 rounded-lg"
              resizeMode="cover"
            />
          ) : item.icon ? (
            <Feather
              name={item.icon as any}
              size={24}
              color={isSelected ? themedColors.iconSelected : themedColors.icon}
              style={{ marginBottom: 8 }}
            />
          ) : item.color ? (
            <View
              className="w-8 h-8 rounded-full mb-2"
              style={{ backgroundColor: item.color }}
            />
          ) : (
            <View
              className="w-8 h-8 rounded-full mb-2"
              style={{ backgroundColor: themedColors.border }}
            />
          )}

          {/* Label */}
          <Text 
            style={{ 
              color: isSelected ? themedColors.textSelected : themedColors.text 
            }}
            className="text-sm font-medium text-center"
            numberOfLines={2}
          >
            {item.label}
          </Text>

          {/* Selection Indicator */}
          {isSelected && (
            <View className="absolute top-2 right-2">
              <Feather 
                name="check-circle"
                size={16}
                color={themedColors.iconSelected}
              />
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const celebrationStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: celebrationScale.value }],
    };
  });

  // Create rows for grid layout
  const rows = [];
  for (let i = 0; i < items.length; i += columns) {
    rows.push(items.slice(i, i + columns));
  }

  return (
    <View className="w-full">
      {/* Title */}
      <Animated.View style={celebrationStyle} className="mb-6">
        <Text 
          style={{ color: themedColors.text }}
          className="text-xl font-bold mb-2"
        >
          {title}
          {isRequired && <Text style={{ color: themedColors.borderSelected }}> *</Text>}
        </Text>
        {subtitle && (
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-base leading-relaxed"
          >
            {subtitle}
          </Text>
        )}
      </Animated.View>

      {/* Grid */}
      <View className="mb-4">
        {rows.map((row, rowIndex) => (
          <View key={rowIndex} className="flex-row">
            {row.map((item, itemIndex) => renderItem(item, rowIndex * columns + itemIndex))}
            {/* Fill empty spaces in last row */}
            {row.length < columns && 
              Array.from({ length: columns - row.length }).map((_, emptyIndex) => (
                <View key={`empty-${emptyIndex}`} className="flex-1 mx-1" />
              ))
            }
          </View>
        ))}
      </View>

      {/* Selection Info */}
      {allowMultiple && (
        <View className="mb-4">
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-sm text-center"
          >
            {selectedItems.length === 0 
              ? `Select up to ${maxSelections} options`
              : `${selectedItems.length}/${maxSelections} selected`
            }
          </Text>
        </View>
      )}

      {/* Validation Message */}
      {isRequired && selectedItems.length === 0 && (
        <Text 
          style={{ color: themedColors.borderSelected }}
          className="text-sm mt-1"
        >
          Please select at least one option
        </Text>
      )}

      {/* Encouragement Message */}
      {selectedItems.length > 0 && (
        <Animated.View 
          entering={withSpring(1)}
          className="mt-2"
        >
          <Text 
            style={{ color: themedColors.borderSelected }}
            className="text-sm font-medium text-center"
          >
            {selectedItems.length === 1 ? "Great choice! 🎉" : 
             allowMultiple && selectedItems.length < maxSelections ? "Feel free to select more! ✨" : 
             "Perfect selection! 🌟"}
          </Text>
        </Animated.View>
      )}
    </View>
  );
}

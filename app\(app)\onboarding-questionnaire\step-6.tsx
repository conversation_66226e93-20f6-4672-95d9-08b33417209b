import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import QuestionnaireLayout from '../../../components/questionnaire/QuestionnaireLayout';
import VisualGrid from '../../../components/questionnaire/QuestionTypes/VisualGrid';
import TextInput from '../../../components/questionnaire/QuestionTypes/TextInput';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { QuestionnaireStep6Data } from '../../../types/questionnaire';
import { useColorScheme } from 'nativewind';

const reactionStyles = [
  { id: 'expressive', label: 'Very Expressive', icon: 'smile', image: require('@/assets/images/onboarding1.png') },
  { id: 'grateful', label: 'Quietly Grateful', icon: 'heart', image: require('@/assets/images/onboarding2.png') },
  { id: 'practical', label: 'Practical & Honest', icon: 'check-circle', image: require('@/assets/images/onboarding3.png') },
  { id: 'surprised', label: 'Loves Surprises', icon: 'gift', image: require('@/assets/images/onboarding1.png') },
];

export default function Step6() {
  const router = useRouter();
  const { data, updateStepData, completeStep, addBadge } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [currentStep, setCurrentStep] = useState(1); // 1: best gift, 2: reaction style
  const [stepData, setStepData] = useState<QuestionnaireStep6Data>({
    giftHistory: {
      bestGiftGiven: data.step6?.giftHistory?.bestGiftGiven || '',
      worstGiftGiven: data.step6?.giftHistory?.worstGiftGiven || '',
      theirReactionStyle: data.step6?.giftHistory?.theirReactionStyle || '',
    },
  });

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return true; // Optional
      case 2:
        return stepData.giftHistory.theirReactionStyle.length > 0;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete step and move to next questionnaire step
      updateStepData(6, stepData);
      completeStep(6);

      // Award gift historian badge
      addBadge({
        id: 'gift-historian',
        name: 'Gift Historian',
        description: 'Shared gift-giving experiences and insights',
        icon: '📚',
        category: 'progress',
        unlockedAt: new Date(),
      });

      router.push('/(app)/onboarding-questionnaire/step-7');
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const updateGiftHistory = (field: keyof QuestionnaireStep6Data['giftHistory'], value: any) => {
    setStepData(prev => ({
      ...prev,
      giftHistory: {
        ...prev.giftHistory,
        [field]: value,
      },
    }));
  };

  const recipientName = data.step1?.recipientName || 'them';

  const renderCurrentQuestion = () => {
    switch (currentStep) {
      case 1:
        return (
          <TextInput
            title={`What's the best gift you've given ${recipientName}?`}
            subtitle="Tell us about a gift that really made them happy (optional)"
            placeholder="e.g., A handmade photo album of our memories together..."
            multiline={true}
            maxLength={200}
            onAnswer={(value) => updateGiftHistory('bestGiftGiven', value)}
            currentAnswer={stepData.giftHistory.bestGiftGiven}
            isRequired={false}
            showSuggestions={true}
            suggestions={[
              'Something handmade',
              'An experience together',
              'Something they mentioned wanting',
              'A surprise that matched their interests',
            ]}
          />
        );

      case 2:
        return (
          <VisualGrid
            title={`How does ${recipientName} typically react to gifts?`}
            subtitle="Understanding their reaction style helps us suggest the right approach"
            items={reactionStyles}
            columns={2}
            allowMultiple={false}
            onAnswer={(value) => updateGiftHistory('theirReactionStyle', value)}
            currentAnswer={stepData.giftHistory.theirReactionStyle}
            isRequired={true}
          />
        );

      default:
        return null;
    }
  };

  const getNextButtonText = () => {
    if (currentStep < 2) return "Continue";
    return "Next Step";
  };

  return (
    <QuestionnaireLayout
      title="Gift History & Reactions"
      subtitle="Learning from past experiences"
      stepNumber={6}
      onNext={handleNext}
      onBack={handleBack}
      nextButtonText={getNextButtonText()}
      isNextDisabled={!isStepValid()}
      showBackButton={true}
      showSkipButton={currentStep === 1}
      onSkip={currentStep === 1 ? () => setCurrentStep(2) : undefined}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        {renderCurrentQuestion()}

        {/* Progress indicator for sub-steps */}
        <View className="mt-6 flex-row justify-center">
          {[1, 2].map((step) => (
            <View
              key={step}
              className={`w-2 h-2 rounded-full mx-1 ${
                step === currentStep
                  ? 'bg-red-500'
                  : step < currentStep
                    ? 'bg-green-500'
                    : isDark
                      ? 'bg-gray-600'
                      : 'bg-gray-300'
              }`}
            />
          ))}
        </View>
      </ScrollView>
    </QuestionnaireLayout>
  );
}

import React, { useState, useEffect } from 'react';
import { View, Text, TextInput as RNTextInput, TouchableOpacity } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { TextInputProps } from '../../../types/questionnaire';
import * as Haptics from 'expo-haptics';

export default function TextInput({
  title,
  subtitle,
  placeholder = "Type your answer...",
  multiline = false,
  maxLength = 100,
  suggestions = [],
  showSuggestions = false,
  onAnswer,
  currentAnswer = "",
  isRequired = false,
}: TextInputProps) {
  const [value, setValue] = useState(currentAnswer);
  const [isFocused, setIsFocused] = useState(false);
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const focusScale = useSharedValue(1);
  const celebrationScale = useSharedValue(1);
  const suggestionOpacity = useSharedValue(0);

  const themedColors = {
    background: isDark ? '#1F2937' : '#F9FAFB',
    border: isDark ? '#374151' : '#E5E7EB',
    borderFocused: '#E5355F',
    text: isDark ? '#FFFFFF' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    placeholder: isDark ? '#6B7280' : '#9CA3AF',
    suggestion: isDark ? '#374151' : '#F3F4F6',
  };

  useEffect(() => {
    onAnswer(value);
  }, [value, onAnswer]);

  useEffect(() => {
    if (showSuggestions && suggestions.length > 0) {
      suggestionOpacity.value = withTiming(isFocused ? 1 : 0, { duration: 300 });
    }
  }, [isFocused, showSuggestions, suggestions.length]);

  const handleFocus = () => {
    setIsFocused(true);
    focusScale.value = withSpring(1.02);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleBlur = () => {
    setIsFocused(false);
    focusScale.value = withSpring(1);
  };

  const handleChangeText = (text: string) => {
    setValue(text);
    
    // Celebration animation for first character
    if (text.length === 1 && currentAnswer.length === 0) {
      celebrationScale.value = withSpring(1.1, {}, () => {
        celebrationScale.value = withSpring(1);
      });
      runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Medium);
    }
  };

  const handleSuggestionPress = (suggestion: string) => {
    setValue(suggestion);
    setIsFocused(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const inputContainerStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: focusScale.value }],
      borderColor: isFocused ? themedColors.borderFocused : themedColors.border,
    };
  });

  const celebrationStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: celebrationScale.value }],
    };
  });

  const suggestionStyle = useAnimatedStyle(() => {
    return {
      opacity: suggestionOpacity.value,
      transform: [
        {
          translateY: interpolate(
            suggestionOpacity.value,
            [0, 1],
            [-10, 0]
          ),
        },
      ],
    };
  });

  return (
    <View className="w-full">
      {/* Title */}
      <Animated.View style={celebrationStyle} className="mb-6">
        <Text 
          style={{ color: themedColors.text }}
          className="text-xl font-bold mb-2"
        >
          {title}
          {isRequired && <Text style={{ color: themedColors.borderFocused }}> *</Text>}
        </Text>
        {subtitle && (
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-base leading-relaxed"
          >
            {subtitle}
          </Text>
        )}
      </Animated.View>

      {/* Input Container */}
      <Animated.View
        style={[
          inputContainerStyle,
          {
            backgroundColor: themedColors.background,
            borderWidth: 2,
            borderRadius: 16,
          },
        ]}
        className="p-4 mb-4"
      >
        <RNTextInput
          value={value}
          onChangeText={handleChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          placeholderTextColor={themedColors.placeholder}
          multiline={multiline}
          maxLength={maxLength}
          style={{
            color: themedColors.text,
            fontSize: 16,
            fontWeight: '500',
            minHeight: multiline ? 80 : 24,
            textAlignVertical: multiline ? 'top' : 'center',
          }}
          autoCapitalize="words"
          autoCorrect={true}
          returnKeyType="done"
        />
        
        {/* Character Count */}
        {maxLength && (
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-xs mt-2 text-right"
          >
            {value.length}/{maxLength}
          </Text>
        )}
      </Animated.View>

      {/* Suggestions */}
      {showSuggestions && suggestions.length > 0 && (
        <Animated.View style={suggestionStyle}>
          <Text 
            style={{ color: themedColors.textSecondary }}
            className="text-sm font-medium mb-2"
          >
            Suggestions:
          </Text>
          <View className="flex-row flex-wrap">
            {suggestions.map((suggestion, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => handleSuggestionPress(suggestion)}
                className="mr-2 mb-2 px-3 py-2 rounded-full"
                style={{ backgroundColor: themedColors.suggestion }}
              >
                <Text 
                  style={{ color: themedColors.text }}
                  className="text-sm font-medium"
                >
                  {suggestion}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>
      )}

      {/* Validation Message */}
      {isRequired && value.trim().length === 0 && !isFocused && (
        <Text 
          style={{ color: themedColors.borderFocused }}
          className="text-sm mt-1"
        >
          This field is required
        </Text>
      )}

      {/* Encouragement Message */}
      {value.length > 0 && (
        <Animated.View 
          entering={withSpring(1)}
          className="mt-2"
        >
          <Text 
            style={{ color: themedColors.borderFocused }}
            className="text-sm font-medium"
          >
            {value.length === 1 ? "Great start! 🎉" : 
             value.length < 3 ? "Keep going! ✨" : 
             "Perfect! 🌟"}
          </Text>
        </Animated.View>
      )}
    </View>
  );
}
